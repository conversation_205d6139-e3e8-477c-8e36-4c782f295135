import 'package:design_system/design_system.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';

import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final brightness = View.of(context).platformDispatcher.platformBrightness;
    final materialTheme = DesignSystem.buildTheme(context);

    return MaterialApp.router(
      title: 'BioMedict',
      theme:
          brightness == Brightness.light
              ? materialTheme.light()
              : materialTheme.dark(),
    );
  }
}
