import 'package:di/di.dart';
import 'package:logger/logger.dart';

// Example service classes
class ApiService {
  final String baseUrl;
  final Logger _logger = Logger();
  bool _isDisposed = false;

  ApiService(this.baseUrl);

  void makeRequest(String endpoint) {
    if (_isDisposed) {
      throw StateError('ApiService has been disposed');
    }
    _logger.i('Making request to: $baseUrl/$endpoint');
  }

  void dispose() {
    _isDisposed = true;
    _logger.i('ApiService disposed');
  }
}

class UserRepository {
  final ApiService _apiService;
  final Logger _logger = Logger();
  bool _isDisposed = false;

  UserRepository(this._apiService);

  void getUser(String id) {
    if (_isDisposed) {
      throw StateError('UserRepository has been disposed');
    }
    _apiService.makeRequest('users/$id');
  }

  void dispose() {
    _isDisposed = true;
    _logger.i('UserRepository disposed');
  }
}

class UserService {
  final UserRepository _repository;
  final Logger _logger = Logger();
  bool _isDisposed = false;

  UserService(this._repository);

  void fetchUserProfile(String userId) {
    if (_isDisposed) {
      throw StateError('UserService has been disposed');
    }
    _logger.i('Fetching user profile for: $userId');
    _repository.getUser(userId);
  }

  void dispose() {
    _isDisposed = true;
    _logger.i('UserService disposed');
  }
}

void main() async {
  final logger = Logger();

  // Create dependency injection instance
  final di = GetItDependencyInjection();

  // Initialize
  await di.init();
  logger.i('Dependency injection initialized');

  try {
    // Register dependencies with dispose functions
    final apiService = ApiService('https://api.example.com');
    di.register<ApiService>(
      apiService,
      dispose: (service) => service.dispose(),
    );

    final userRepository = UserRepository(di.get<ApiService>());
    di.register<UserRepository>(
      userRepository,
      dispose: (repo) => repo.dispose(),
    );

    final userService = UserService(di.get<UserRepository>());
    di.register<UserService>(
      userService,
      dispose: (service) => service.dispose(),
    );

    logger.i('All dependencies registered successfully!');

    // Use the services
    logger.i('--- Using services ---');
    final service = di.get<UserService>();
    service.fetchUserProfile('123');

    // Check what's registered
    logger.i('--- Checking registrations ---');
    logger.i('ApiService registered: ${di.has<ApiService>()}');
    logger.i('UserRepository registered: ${di.has<UserRepository>()}');
    logger.i('UserService registered: ${di.has<UserService>()}');

    // Unregister a specific service
    logger.i('--- Unregistering UserService ---');
    await di.unregister<UserService>(null);
    logger.i('UserService registered: ${di.has<UserService>()}');

    // Reset everything
    logger.i('--- Resetting all dependencies ---');
    await di.reset();
    logger.i('ApiService registered: ${di.has<ApiService>()}');
    logger.i('UserRepository registered: ${di.has<UserRepository>()}');
    logger.i('UserService registered: ${di.has<UserService>()}');

  } finally {
    // Clean up
    await di.dispose();
    logger.i('Dependency injection disposed');
  }
}
