import 'package:flutter_test/flutter_test.dart';
import 'package:auth/auth.dart';

void main() {
  group('Auth Package Tests', () {
    group('AuthUser Model', () {
      test('should create AuthUser with required fields', () {
        final user = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        expect(user.uid, 'test-uid');
        expect(user.email, '<EMAIL>');
        expect(user.emailVerified, true);
        expect(user.isAnonymous, false);
        expect(user.providerIds, ['password']);
      });

      test('should generate correct initials', () {
        final user1 = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        final user2 = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: '<PERSON>',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        final user3 = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        expect(user1.initials, 'JD');
        expect(user2.initials, 'JS');
        expect(user3.initials, 'T'); // First letter of email
      });

      test('should return correct full name', () {
        final user1 = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        final user2 = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Jane Smith',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        expect(user1.fullName, 'John Doe');
        expect(user2.fullName, 'Jane Smith');
      });

      test('should check complete profile correctly', () {
        final completeUser = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        final incompleteUser = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          emailVerified: false,
          isAnonymous: false,
          providerIds: ['password'],
        );

        expect(completeUser.hasCompleteProfile, true);
        expect(incompleteUser.hasCompleteProfile, false);
      });

      test('should copy with updated fields', () {
        final originalUser = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          firstName: 'John',
          emailVerified: false,
          isAnonymous: false,
          providerIds: ['password'],
        );

        final updatedUser = originalUser.copyWith(
          lastName: 'Doe',
          emailVerified: true,
        );

        expect(updatedUser.uid, originalUser.uid);
        expect(updatedUser.email, originalUser.email);
        expect(updatedUser.firstName, originalUser.firstName);
        expect(updatedUser.lastName, 'Doe');
        expect(updatedUser.emailVerified, true);
      });
    });

    group('AuthException', () {
      test('should create exception with message and code', () {
        final exception = AuthException(
          message: 'Test error',
          code: 'test-error',
        );

        expect(exception.message, 'Test error');
        expect(exception.code, 'test-error');
      });

      test('should create predefined exceptions', () {
        final networkError = AuthException.network();
        final genericError = AuthException.generic();
        final invalidCredentials = AuthException.invalidCredentials();

        expect(networkError.code, 'network-error');
        expect(genericError.code, 'generic-error');
        expect(invalidCredentials.code, 'invalid-credentials');
      });

      test('should check error types correctly', () {
        final networkError = AuthException.network();
        final credentialError = AuthException.invalidCredentials();
        final rateLimitError = AuthException.tooManyRequests();

        expect(networkError.isNetworkError, true);
        expect(credentialError.isCredentialError, true);
        expect(rateLimitError.isRateLimitError, true);
      });
    });

    group('AuthState Extensions', () {
      test('should check state types correctly', () {
        const initialState = AuthStateInitial();
        const loadingState = AuthStateLoading();
        const unauthenticatedState = AuthStateUnauthenticated();
        const errorState = AuthStateError(message: 'Test error', code: 'test');

        expect(initialState.isInitial, true);
        expect(loadingState.isLoading, true);
        expect(unauthenticatedState.isUnauthenticated, true);
        expect(errorState.isError, true);
      });

      test('should return user from authenticated state', () {
        final user = AuthUser(
          uid: 'test-uid',
          email: '<EMAIL>',
          emailVerified: true,
          isAnonymous: false,
          providerIds: ['password'],
        );

        final authenticatedState = AuthStateAuthenticated(user: user);
        final unauthenticatedState = AuthStateUnauthenticated();

        expect(authenticatedState.user, user);
        expect(unauthenticatedState.user, null);
      });
    });

    group('AuthRoutes', () {
      test('should identify route types correctly', () {
        expect(AuthRoutes.isProtectedRoute('/dashboard'), true);
        expect(AuthRoutes.isProtectedRoute('/profile'), true);
        expect(AuthRoutes.isAuthRoute('/auth/sign-in'), true);
        expect(AuthRoutes.isAuthRoute('/auth/sign-up'), true);
        expect(AuthRoutes.isPublicRoute('/legal/terms'), true);
        expect(AuthRoutes.isPublicRoute('/legal/privacy'), true);
      });

      test('should return correct default routes', () {
        expect(AuthRoutes.defaultAuthenticatedRoute, '/dashboard');
        expect(AuthRoutes.defaultUnauthenticatedRoute, '/auth/sign-in');
      });
    });

    group('AuthUserMapper', () {
      test('should create test user with default values', () {
        const mapper = AuthUserMapper();
        final testUser = mapper.createTestUser();

        expect(testUser.uid, 'test-uid');
        expect(testUser.email, '<EMAIL>');
        expect(testUser.emailVerified, true);
        expect(testUser.isAnonymous, false);
        expect(testUser.providerIds, ['password']);
      });

      test('should create test user with custom values', () {
        const mapper = AuthUserMapper();
        final testUser = mapper.createTestUser(
          uid: 'custom-uid',
          email: '<EMAIL>',
          firstName: 'Custom',
          lastName: 'User',
        );

        expect(testUser.uid, 'custom-uid');
        expect(testUser.email, '<EMAIL>');
        expect(testUser.firstName, 'Custom');
        expect(testUser.lastName, 'User');
      });

      test('should merge user data correctly', () {
        const mapper = AuthUserMapper();
        final baseUser = mapper.createTestUser(
          firstName: 'John',
          lastName: 'Doe',
        );

        final updates = {
          'firstName': 'Jane',
          'photoUrl': 'https://example.com/photo.jpg',
        };

        final mergedUser = mapper.mergeUserData(baseUser, updates);

        expect(mergedUser.firstName, 'Jane');
        expect(mergedUser.lastName, 'Doe'); // Should remain unchanged
        expect(mergedUser.photoUrl, 'https://example.com/photo.jpg');
      });
    });
  });
}
