# API Documentation

This document provides detailed API documentation for the Firebase Authentication package.

## Core Classes

### AuthDI

Dependency injection setup and service locator.

```dart
class AuthDI {
  /// Initialize authentication dependencies
  static Future<void> initialize({
    DependencyInjection? di,
    FirebaseAuth? firebaseAuth,
    Logger? logger,
  });
  
  /// Get a dependency from the container
  static T get<T extends Object>();
  
  /// Check if a dependency is registered
  static bool has<T extends Object>();
  
  /// Dispose all dependencies
  static Future<void> dispose();
  
  /// Quick access to auth store
  static AuthStore get authStore;
  
  /// Quick access to auth service
  static AuthService get authService;
  
  /// Quick access to auth guard
  static AuthGuard get authGuard;
}
```

### AuthService

High-level authentication operations.

```dart
class AuthService {
  /// Current authenticated user
  AuthUser? get currentUser;
  
  /// Stream of authentication state changes
  Stream<AuthUser?> get authStateChanges;
  
  /// Check if user is authenticated
  bool get isAuthenticated;
  
  /// Check if user's email is verified
  bool get isEmailVerified;
  
  /// Sign in with email and password
  Future<AuthUser> signIn({
    required String email,
    required String password,
  });
  
  /// Create new user account
  Future<AuthUser> signUp({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  });
  
  /// Sign out current user
  Future<void> signOut();
  
  /// Send password reset email
  Future<void> sendPasswordResetEmail({
    required String email,
  });
  
  /// Send email verification
  Future<void> sendEmailVerification();
  
  /// Reload current user data
  Future<void> reloadUser();
  
  /// Update user profile
  Future<AuthUser> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
  });
  
  /// Check if email is registered
  Future<bool> isEmailRegistered({
    required String email,
  });
  
  /// Clear authentication errors
  void clearError();
}
```

### AuthStore

MobX store for reactive state management.

```dart
class AuthStore {
  /// Current authentication state
  @observable
  AuthState authState;
  
  /// Last error message
  @observable
  String? lastErrorMessage;
  
  /// Loading state
  @observable
  bool isLoading;
  
  /// Loading message
  @observable
  String? loadingMessage;
  
  /// Computed: is user authenticated
  @computed
  bool get isAuthenticated;
  
  /// Computed: is user unauthenticated
  @computed
  bool get isUnauthenticated;
  
  /// Computed: is email verification required
  @computed
  bool get isEmailVerificationRequired;
  
  /// Computed: has error
  @computed
  bool get hasError;
  
  /// Computed: current user
  @computed
  AuthUser? get currentUser;
  
  /// Actions for authentication operations
  @action
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  });
  
  @action
  Future<void> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  });
  
  @action
  Future<void> signOut();
  
  @action
  Future<void> sendPasswordResetEmail({
    required String email,
  });
  
  @action
  Future<void> sendEmailVerification();
  
  @action
  Future<void> reloadUser();
  
  @action
  void clearError();
}
```

### AuthGuard

Route protection and navigation logic.

```dart
class AuthGuard {
  /// Redirect logic for protected routes
  String? redirectLogic(BuildContext context, GoRouterState state);
  
  /// Check if a route requires authentication
  bool requiresAuth(String route);
  
  /// Check if user can access a specific route
  bool canAccess(String route);
  
  /// Get appropriate redirect route for current auth state
  String getRedirectRoute();
  
  /// Create GoRouter redirect function
  String? Function(BuildContext, GoRouterState) get redirect;
}
```

## Models

### AuthUser

User model with profile information.

```dart
class AuthUser {
  final String uid;
  final String email;
  final String? displayName;
  final String? firstName;
  final String? lastName;
  final String? photoUrl;
  final bool emailVerified;
  final bool isAnonymous;
  final List<String> providerIds;
  final String? phoneNumber;
  final DateTime? createdAt;
  final DateTime? lastSignInAt;
  final Map<String, dynamic>? metadata;
  
  /// User's full name if available
  String? get fullName;
  
  /// User's initials for display
  String get initials;
  
  /// Whether user has complete profile
  bool get hasCompleteProfile;
  
  /// Primary authentication provider
  String get primaryProvider;
  
  /// Create copy with updated fields
  AuthUser copyWith({...});
  
  /// JSON serialization
  factory AuthUser.fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson();
}
```

### AuthState

Authentication state representation.

```dart
abstract class AuthState {
  const AuthState();
}

class AuthStateInitial extends AuthState;
class AuthStateLoading extends AuthState;
class AuthStateAuthenticated extends AuthState {
  final AuthUser user;
}
class AuthStateUnauthenticated extends AuthState;
class AuthStateEmailVerificationRequired extends AuthState {
  final AuthUser user;
}
class AuthStateError extends AuthState {
  final String message;
  final String? code;
  final dynamic error;
}

/// Extension methods for type checking
extension AuthStateExtensions on AuthState {
  bool get isInitial;
  bool get isLoading;
  bool get isAuthenticated;
  bool get isUnauthenticated;
  bool get isEmailVerificationRequired;
  bool get isError;
  AuthUser? get user;
  String? get errorMessage;
}
```

### AuthException

Structured error handling.

```dart
class AuthException implements Exception {
  final String message;
  final String code;
  final dynamic originalException;
  final StackTrace? stackTrace;
  
  /// Create from Firebase Auth error code
  factory AuthException.fromFirebaseAuthCode(String code, [dynamic originalException]);
  
  /// Create generic exception
  factory AuthException.generic([String? message]);
  
  /// Create network exception
  factory AuthException.network([String? message]);
  
  /// Predefined exceptions
  factory AuthException.invalidCredentials();
  factory AuthException.emailVerificationRequired();
  factory AuthException.userNotFound();
  factory AuthException.emailAlreadyInUse();
  factory AuthException.weakPassword();
  factory AuthException.tooManyRequests();
  
  /// Error type checks
  bool get isNetworkError;
  bool get isCredentialError;
  bool get isRateLimitError;
  bool get requiresUserAction;
}
```

## UI Components

### AuthTextField

Styled text input field for authentication forms.

```dart
class AuthTextField extends StatelessWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final bool enabled;
  final String? semanticLabel;
}

/// Specialized email field
class AuthEmailField extends StatelessWidget;

/// Specialized password field with visibility toggle
class AuthPasswordField extends StatefulWidget;
```

### AuthButton

Consistent button styling for authentication actions.

```dart
class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isSecondary;
  final IconData? icon;
  final double? width;
  final double height;
  final String? semanticLabel;
}

/// Text button variant
class AuthTextButton extends StatelessWidget;

/// Icon button variant
class AuthIconButton extends StatelessWidget;
```

### AuthErrorWidget

Error display component.

```dart
class AuthErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;
  final VoidCallback? onRetry;
  final IconData? icon;
  final bool showDismissButton;
  final bool showRetryButton;
}

/// Snackbar variant
class AuthErrorSnackBar extends SnackBar;

/// Dialog variant
class AuthErrorDialog extends StatelessWidget;
```

### AuthLoadingOverlay

Loading state overlay.

```dart
class AuthLoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingMessage;
  final Color? overlayColor;
  final double overlayOpacity;
}

/// Simple loading indicator
class AuthLoadingIndicator extends StatelessWidget;

/// Linear progress indicator
class AuthLinearLoadingIndicator extends StatelessWidget;

/// Shimmer loading effect
class AuthShimmerLoading extends StatefulWidget;
```

## Constants

### AuthStrings

Centralized string constants.

```dart
class AuthStrings {
  // App information
  static const String appName = 'BiomeDict';
  static const String appDescription = 'Your personal health companion';
  
  // Authentication actions
  static const String signIn = 'Sign In';
  static const String signUp = 'Sign Up';
  static const String signOut = 'Sign Out';
  static const String forgotPassword = 'Forgot Password?';
  
  // Form fields
  static const String email = 'Email';
  static const String password = 'Password';
  static const String firstName = 'First Name';
  static const String lastName = 'Last Name';
  
  // Validation messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email address';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort = 'Password must be at least 6 characters';
  
  // Error messages
  static const String genericError = 'An error occurred. Please try again.';
  static const String networkError = 'Network error. Please check your connection.';
  static const String userNotFound = 'No user found with this email address.';
  
  // Success messages
  static const String signInSuccess = 'Signed in successfully';
  static const String signUpSuccess = 'Account created successfully';
  
  // Loading states
  static const String signingIn = 'Signing in...';
  static const String signingUp = 'Creating account...';
}
```

### AuthRoutes

Route path constants.

```dart
class AuthRoutes {
  // Authentication routes
  static const String signIn = '/auth/sign-in';
  static const String signUp = '/auth/sign-up';
  static const String forgotPassword = '/auth/forgot-password';
  static const String emailVerification = '/auth/email-verification';
  
  // Protected routes
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String settings = '/settings';
  
  // Route lists
  static const List<String> protectedRoutes = [...];
  static const List<String> authRoutes = [...];
  static const List<String> publicRoutes = [...];
  
  // Helper methods
  static bool isProtectedRoute(String route);
  static bool isAuthRoute(String route);
  static bool isPublicRoute(String route);
  static String get defaultAuthenticatedRoute;
  static String get defaultUnauthenticatedRoute;
}
```

## Router Integration

### AuthRouter

Route configuration for authentication.

```dart
class AuthRouter {
  /// Get all authentication routes
  static List<RouteBase> get routes;
  
  /// Create GoRouter with auth integration
  static GoRouter createRouter({
    List<RouteBase>? additionalRoutes,
    String? initialLocation,
    GlobalKey<NavigatorState>? navigatorKey,
    String Function(BuildContext, GoRouterState)? redirect,
  });
  
  /// Navigation helpers
  static void goToSignIn(BuildContext context);
  static void goToSignUp(BuildContext context);
  static void goToForgotPassword(BuildContext context);
  static void navigateBasedOnAuthState(BuildContext context);
}
```

## Extension Methods

### AuthNavigationExtension

Navigation helpers for BuildContext.

```dart
extension AuthNavigationExtension on BuildContext {
  void goToSignIn();
  void goToSignUp();
  void goToForgotPassword();
  void goToEmailVerification();
  void goBackSafely();
  void navigateByAuthState();
}
```

### AuthErrorExtension

Error display helpers for BuildContext.

```dart
extension AuthErrorExtension on BuildContext {
  void showAuthError(String message, {VoidCallback? onRetry});
  Future<void> showAuthErrorDialog(String message, {String title, VoidCallback? onRetry});
}
```

## Usage Examples

### Basic Authentication Flow

```dart
// Sign in
try {
  final user = await AuthDI.authService.signIn(
    email: '<EMAIL>',
    password: 'password123',
  );
  print('Signed in: ${user.email}');
} on AuthException catch (e) {
  print('Sign in failed: ${e.message}');
}

// Sign up
try {
  final user = await AuthDI.authService.signUp(
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'John',
    lastName: 'Doe',
  );
  print('Account created: ${user.email}');
} on AuthException catch (e) {
  print('Sign up failed: ${e.message}');
}

// Sign out
await AuthDI.authService.signOut();
```

### Reactive UI with MobX

```dart
Observer(
  builder: (context) {
    final authStore = AuthDI.authStore;
    
    return Column(
      children: [
        if (authStore.isLoading)
          CircularProgressIndicator(),
        
        if (authStore.hasError)
          AuthErrorWidget(
            message: authStore.lastErrorMessage!,
            onDismiss: authStore.clearError,
          ),
        
        if (authStore.isAuthenticated)
          Text('Welcome, ${authStore.currentUser!.email}!'),
        
        if (authStore.isUnauthenticated)
          AuthButton(
            text: 'Sign In',
            onPressed: () => context.goToSignIn(),
          ),
      ],
    );
  },
)
```
