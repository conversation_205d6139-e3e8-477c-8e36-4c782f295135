# Firebase Authentication Package

A comprehensive Firebase Authentication package for BiomeDict with MobX state management, go_router integration, and Material Design UI components.

[![nonstop_cli](https://img.shields.io/badge/started%20with-nonstop_cli-166C4E.svg?style=flat-square)](https://pub.dev/packages/nonstop_cli)
[![melos](https://img.shields.io/badge/maintained%20with-melos-f700ff.svg?style=flat-square)](https://github.com/invertase/melos)

## Features

- 🔐 **Firebase Authentication** - Email/password authentication with Firebase
- 📧 **Email Verification** - Complete email verification flow
- 🔄 **Password Reset** - Secure password reset functionality
- 📱 **Material Design UI** - Beautiful, accessible UI components
- 🎯 **MobX State Management** - Reactive state management
- 🛣️ **go_router Integration** - Route guards and navigation
- 🏗️ **Clean Architecture** - Repository pattern and dependency injection
- 🧪 **Comprehensive Testing** - Unit and widget tests
- 📚 **Full Documentation** - Complete API documentation

## Installation 💻

Add to your `pubspec.yaml`:

```yaml
dependencies:
  auth:
    path: ../../packages/auth
```

## Quick Start

### 1. Initialize Dependencies

```dart
import 'package:auth/auth.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  // Initialize auth dependencies
  await AuthDI.initialize();

  runApp(MyApp());
}
```

### 2. Set up Router with Auth Guards

```dart
import 'package:auth/auth.dart';

final router = GoRouter(
  initialLocation: AuthRoutes.signIn,
  redirect: (context, state) {
    return AuthDI.authGuard.redirectLogic(context, state);
  },
  routes: [
    // Include auth routes
    ...AuthRouter.routes,

    // Your app routes
    GoRoute(
      path: '/dashboard',
      builder: (context, state) => DashboardScreen(),
    ),
  ],
);
```

### 3. Use Authentication in Your App

```dart
import 'package:auth/auth.dart';

class LoginButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () async {
        try {
          await AuthDI.authService.signIn(
            email: '<EMAIL>',
            password: 'password123',
          );
        } catch (e) {
          // Handle error
        }
      },
      child: Text('Sign In'),
    );
  }
}
```

## Architecture

The package follows clean architecture principles:

```
lib/
├── src/
│   ├── core/
│   │   ├── constants/          # String and route constants
│   │   └── di/                 # Dependency injection setup
│   ├── features/auth/
│   │   ├── data/               # Data layer (repositories, mappers)
│   │   ├── domain/             # Domain layer (models, interfaces)
│   │   └── presentation/       # UI layer (screens, widgets, stores)
│   └── shared/                 # Shared utilities
└── auth.dart                   # Main export file
```

## State Management

The package uses MobX for reactive state management:

```dart
// Observe authentication state
Observer(
  builder: (context) {
    final authStore = AuthDI.authStore;

    if (authStore.isLoading) {
      return CircularProgressIndicator();
    }

    if (authStore.isAuthenticated) {
      return WelcomeScreen();
    }

    return SignInScreen();
  },
)
```

## Route Protection

Routes are automatically protected based on authentication state:

```dart
// Protected routes require authentication
static const List<String> protectedRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
];

// Auth routes redirect authenticated users
static const List<String> authRoutes = [
  '/auth/sign-in',
  '/auth/sign-up',
  '/auth/forgot-password',
];
```

## Customization

### Custom Error Handling

```dart
class CustomAuthService extends AuthService {
  @override
  Future<AuthUser> signIn({
    required String email,
    required String password,
  }) async {
    try {
      return await super.signIn(email: email, password: password);
    } on AuthException catch (e) {
      // Custom error handling
      throw CustomAuthException(e.message);
    }
  }
}
```

### Custom UI Components

```dart
class CustomSignInScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Use provided components
          AuthTextField(
            labelText: 'Email',
            prefixIcon: Icons.email,
          ),
          AuthButton(
            text: 'Sign In',
            onPressed: () => _handleSignIn(),
          ),
        ],
      ),
    );
  }
}
```

## API Reference

### Core Classes

- **`AuthDI`** - Dependency injection setup
- **`AuthService`** - High-level authentication operations
- **`AuthStore`** - MobX store for state management
- **`AuthGuard`** - Route protection logic
- **`AuthRouter`** - Route configuration

### Models

- **`AuthUser`** - User model with profile information
- **`AuthState`** - Authentication state representation
- **`AuthException`** - Structured error handling

### UI Components

- **`AuthTextField`** - Styled text input fields
- **`AuthButton`** - Consistent button styling
- **`AuthErrorWidget`** - Error display component
- **`AuthLoadingOverlay`** - Loading state overlay

## Testing

Run tests with:

```bash
flutter test
```

The package includes:
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for authentication flows

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.