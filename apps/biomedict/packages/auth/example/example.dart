import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:auth/auth.dart';

/// Example app demonstrating the Firebase Authentication package usage.
/// 
/// This example shows how to integrate the auth package into a Flutter app
/// with proper initialization, routing, and state management.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase (you'll need to add your own firebase_options.dart)
  await Firebase.initializeApp();
  
  // Initialize auth dependencies
  await AuthDI.initialize();
  
  runApp(const ExampleApp());
}

class ExampleApp extends StatelessWidget {
  const ExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Auth Package Example',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const AuthStateListener(),
    );
  }
}

/// Widget that listens to authentication state and shows appropriate screens
class AuthStateListener extends StatelessWidget {
  const AuthStateListener({super.key});

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        final authStore = AuthDI.authStore;
        
        // Show loading while checking auth state
        if (authStore.authState.isInitial || authStore.authState.isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        // Show email verification screen if needed
        if (authStore.authState.isEmailVerificationRequired) {
          return AuthEmailVerificationScreen(authStore: authStore);
        }
        
        // Show main app if authenticated
        if (authStore.authState.isAuthenticated) {
          return const MainAppScreen();
        }
        
        // Show sign in screen if not authenticated
        return AuthSignInScreen(authStore: authStore);
      },
    );
  }
}

/// Main application screen shown to authenticated users
class MainAppScreen extends StatelessWidget {
  const MainAppScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = AuthDI.authService.currentUser;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Auth Example App'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _handleSignOut(context),
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome message
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome!',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'You are successfully signed in to the Auth Package Example.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // User information
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User Information',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    _buildUserInfoRow('Email', user?.email ?? 'Not available'),
                    _buildUserInfoRow('Name', user?.fullName ?? 'Not provided'),
                    _buildUserInfoRow('Email Verified', user?.emailVerified == true ? 'Yes' : 'No'),
                    _buildUserInfoRow('User ID', user?.uid ?? 'Not available'),
                    _buildUserInfoRow('Provider', user?.primaryProvider ?? 'Unknown'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Actions',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _handleReloadUser(context),
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reload User Data'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (user?.emailVerified == false)
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _handleSendVerification(context),
                          icon: const Icon(Icons.email),
                          label: const Text('Send Email Verification'),
                        ),
                      ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () => _handleSignOut(context),
                        icon: const Icon(Icons.logout),
                        label: const Text('Sign Out'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const Spacer(),
            
            // Auth state observer
            Observer(
              builder: (context) {
                final authStore = AuthDI.authStore;
                
                if (authStore.isLoading) {
                  return Card(
                    color: Colors.blue.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            authStore.loadingMessage ?? 'Loading...',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  );
                }
                
                if (authStore.hasError) {
                  return AuthErrorWidget(
                    message: authStore.lastErrorMessage ?? 'An error occurred',
                    onDismiss: authStore.clearError,
                  );
                }
                
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _handleReloadUser(BuildContext context) async {
    try {
      await AuthDI.authService.reloadUser();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User data reloaded successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reload user data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleSendVerification(BuildContext context) async {
    try {
      await AuthDI.authService.sendEmailVerification();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Verification email sent'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send verification email: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleSignOut(BuildContext context) async {
    try {
      await AuthDI.authService.signOut();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Signed out successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to sign out: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
