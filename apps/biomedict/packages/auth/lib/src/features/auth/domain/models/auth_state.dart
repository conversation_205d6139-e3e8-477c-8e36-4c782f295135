import 'package:equatable/equatable.dart';
import 'auth_user.dart';

/// Represents the current authentication state of the application.
/// 
/// This enum-like class provides a type-safe way to handle different
/// authentication states throughout the application.
abstract class AuthState extends Equatable {
  const AuthState();
}

/// Initial state when the authentication status is unknown
class AuthStateInitial extends AuthState {
  const AuthStateInitial();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'AuthStateInitial()';
}

/// State when authentication is being checked or an auth operation is in progress
class AuthStateLoading extends AuthState {
  final String? message;

  const AuthStateLoading({this.message});

  @override
  List<Object?> get props => [message];

  @override
  String toString() => 'AuthStateLoading(message: $message)';
}

/// State when user is authenticated
class AuthStateAuthenticated extends AuthState {
  final AuthUser user;

  const AuthStateAuthenticated({required this.user});

  @override
  List<Object?> get props => [user];

  @override
  String toString() => 'AuthStateAuthenticated(user: ${user.email})';
}

/// State when user is not authenticated
class AuthStateUnauthenticated extends AuthState {
  const AuthStateUnauthenticated();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'AuthStateUnauthenticated()';
}

/// State when user's email needs verification
class AuthStateEmailVerificationRequired extends AuthState {
  final AuthUser user;

  const AuthStateEmailVerificationRequired({required this.user});

  @override
  List<Object?> get props => [user];

  @override
  String toString() => 'AuthStateEmailVerificationRequired(user: ${user.email})';
}

/// State when an authentication error has occurred
class AuthStateError extends AuthState {
  final String message;
  final String? code;
  final dynamic error;

  const AuthStateError({
    required this.message,
    this.code,
    this.error,
  });

  @override
  List<Object?> get props => [message, code, error];

  @override
  String toString() => 'AuthStateError(message: $message, code: $code)';
}

/// Extension methods for AuthState to provide convenient type checking
extension AuthStateExtensions on AuthState {
  /// Returns true if the state is initial
  bool get isInitial => this is AuthStateInitial;

  /// Returns true if the state is loading
  bool get isLoading => this is AuthStateLoading;

  /// Returns true if the user is authenticated
  bool get isAuthenticated => this is AuthStateAuthenticated;

  /// Returns true if the user is not authenticated
  bool get isUnauthenticated => this is AuthStateUnauthenticated;

  /// Returns true if email verification is required
  bool get isEmailVerificationRequired => this is AuthStateEmailVerificationRequired;

  /// Returns true if there's an error
  bool get isError => this is AuthStateError;

  /// Returns the authenticated user if available
  AuthUser? get user {
    if (this is AuthStateAuthenticated) {
      return (this as AuthStateAuthenticated).user;
    }
    if (this is AuthStateEmailVerificationRequired) {
      return (this as AuthStateEmailVerificationRequired).user;
    }
    return null;
  }

  /// Returns the error message if available
  String? get errorMessage {
    if (this is AuthStateError) {
      return (this as AuthStateError).message;
    }
    return null;
  }

  /// Returns the error code if available
  String? get errorCode {
    if (this is AuthStateError) {
      return (this as AuthStateError).code;
    }
    return null;
  }

  /// Returns the loading message if available
  String? get loadingMessage {
    if (this is AuthStateLoading) {
      return (this as AuthStateLoading).message;
    }
    return null;
  }
}
