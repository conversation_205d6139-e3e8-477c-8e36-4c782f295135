import 'package:equatable/equatable.dart';

/// Custom exception class for authentication-related errors.
/// 
/// This class provides a structured way to handle and categorize
/// authentication errors throughout the application.
class AuthException extends Equatable implements Exception {
  /// Error message describing what went wrong
  final String message;

  /// Error code for programmatic handling
  final String code;

  /// Original exception that caused this error (if any)
  final dynamic originalException;

  /// Stack trace for debugging purposes
  final StackTrace? stackTrace;

  const AuthException({
    required this.message,
    required this.code,
    this.originalException,
    this.stackTrace,
  });

  /// Creates an AuthException from a Firebase Auth error code
  factory AuthException.fromFirebaseAuthCode(String code, [dynamic originalException]) {
    final message = _getMessageFromCode(code);
    return AuthException(
      message: message,
      code: code,
      originalException: originalException,
    );
  }

  /// Creates a generic AuthException
  factory AuthException.generic([String? message]) {
    return AuthException(
      message: message ?? 'An unexpected error occurred',
      code: 'generic-error',
    );
  }

  /// Creates a network-related AuthException
  factory AuthException.network([String? message]) {
    return AuthException(
      message: message ?? 'Network error. Please check your connection.',
      code: 'network-error',
    );
  }

  /// Creates an AuthException for invalid credentials
  factory AuthException.invalidCredentials() {
    return const AuthException(
      message: 'Invalid email or password. Please try again.',
      code: 'invalid-credentials',
    );
  }

  /// Creates an AuthException for email verification required
  factory AuthException.emailVerificationRequired() {
    return const AuthException(
      message: 'Please verify your email address before continuing.',
      code: 'email-verification-required',
    );
  }

  /// Creates an AuthException for user not found
  factory AuthException.userNotFound() {
    return const AuthException(
      message: 'No user found with this email address.',
      code: 'user-not-found',
    );
  }

  /// Creates an AuthException for email already in use
  factory AuthException.emailAlreadyInUse() {
    return const AuthException(
      message: 'An account already exists with this email address.',
      code: 'email-already-in-use',
    );
  }

  /// Creates an AuthException for weak password
  factory AuthException.weakPassword() {
    return const AuthException(
      message: 'Password is too weak. Please choose a stronger password.',
      code: 'weak-password',
    );
  }

  /// Creates an AuthException for too many requests
  factory AuthException.tooManyRequests() {
    return const AuthException(
      message: 'Too many requests. Please try again later.',
      code: 'too-many-requests',
    );
  }

  /// Maps Firebase Auth error codes to user-friendly messages
  static String _getMessageFromCode(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Invalid email address format.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      case 'invalid-credential':
        return 'Invalid credentials. Please check your email and password.';
      case 'email-verification-required':
        return 'Please verify your email address before continuing.';
      case 'requires-recent-login':
        return 'This operation requires recent authentication. Please sign in again.';
      case 'credential-already-in-use':
        return 'This credential is already associated with another account.';
      case 'invalid-verification-code':
        return 'Invalid verification code. Please try again.';
      case 'invalid-verification-id':
        return 'Invalid verification ID. Please try again.';
      case 'session-expired':
        return 'Your session has expired. Please sign in again.';
      default:
        return 'An error occurred. Please try again.';
    }
  }

  /// Returns whether this is a network-related error
  bool get isNetworkError {
    return code == 'network-error' || code == 'network-request-failed';
  }

  /// Returns whether this is a credential-related error
  bool get isCredentialError {
    return [
      'user-not-found',
      'wrong-password',
      'invalid-credential',
      'invalid-credentials',
      'invalid-email',
    ].contains(code);
  }

  /// Returns whether this is a rate limiting error
  bool get isRateLimitError {
    return code == 'too-many-requests';
  }

  /// Returns whether this error requires user action
  bool get requiresUserAction {
    return [
      'email-verification-required',
      'requires-recent-login',
      'weak-password',
    ].contains(code);
  }

  @override
  List<Object?> get props => [message, code, originalException];

  @override
  String toString() {
    return 'AuthException(code: $code, message: $message)';
  }
}
