// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthUser _$AuthUserFromJson(Map<String, dynamic> json) => AuthUser(
  uid: json['uid'] as String,
  email: json['email'] as String,
  displayName: json['displayName'] as String?,
  firstName: json['firstName'] as String?,
  lastName: json['lastName'] as String?,
  photoUrl: json['photoUrl'] as String?,
  emailVerified: json['emailVerified'] as bool,
  isAnonymous: json['isAnonymous'] as bool,
  providerIds:
      (json['providerIds'] as List<dynamic>).map((e) => e as String).toList(),
  phoneNumber: json['phoneNumber'] as String?,
  createdAt:
      json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
  lastSignInAt:
      json['lastSignInAt'] == null
          ? null
          : DateTime.parse(json['lastSignInAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AuthUserToJson(AuthUser instance) => <String, dynamic>{
  'uid': instance.uid,
  'email': instance.email,
  'displayName': instance.displayName,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'photoUrl': instance.photoUrl,
  'emailVerified': instance.emailVerified,
  'isAnonymous': instance.isAnonymous,
  'providerIds': instance.providerIds,
  'phoneNumber': instance.phoneNumber,
  'createdAt': instance.createdAt?.toIso8601String(),
  'lastSignInAt': instance.lastSignInAt?.toIso8601String(),
  'metadata': instance.metadata,
};
