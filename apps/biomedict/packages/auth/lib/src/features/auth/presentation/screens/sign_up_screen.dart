import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';

import '../stores/auth_store.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_error_widget.dart';
import '../widgets/auth_loading_overlay.dart';
import '../../../core/constants/auth_strings.dart';
import '../../../core/constants/auth_routes.dart';

/// Sign up screen for user registration.
/// 
/// This screen provides email/password registration functionality with
/// proper validation, error handling, and loading states.
class AuthSignUpScreen extends StatefulWidget {
  final AuthStore authStore;

  const AuthSignUpScreen({
    super.key,
    required this.authStore,
  });

  @override
  State<AuthSignUpScreen> createState() => _AuthSignUpScreenState();
}

class _AuthSignUpScreenState extends State<AuthSignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  final _firstNameFocusNode = FocusNode();
  final _lastNameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    
    _firstNameFocusNode.dispose();
    _lastNameFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AuthStrings.signUp),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Observer(
        builder: (context) => AuthLoadingOverlay(
          isLoading: widget.authStore.isLoading,
          loadingMessage: widget.authStore.loadingMessage,
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildHeader(),
                    const SizedBox(height: 32),
                    _buildSignUpForm(),
                    const SizedBox(height: 16),
                    _buildTermsCheckbox(),
                    const SizedBox(height: 24),
                    _buildErrorWidget(),
                    const SizedBox(height: 24),
                    _buildSignUpButton(),
                    const SizedBox(height: 32),
                    _buildSignInPrompt(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.person_add_outlined,
          size: 64,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 16),
        Text(
          AuthStrings.createAccount,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Join ${AuthStrings.appName} today',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSignUpForm() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: AuthTextField(
                controller: _firstNameController,
                focusNode: _firstNameFocusNode,
                labelText: AuthStrings.firstName,
                hintText: AuthStrings.firstNameHint,
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.person_outlined,
                validator: _validateFirstName,
                onFieldSubmitted: (_) => _lastNameFocusNode.requestFocus(),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AuthTextField(
                controller: _lastNameController,
                focusNode: _lastNameFocusNode,
                labelText: AuthStrings.lastName,
                hintText: AuthStrings.lastNameHint,
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.person_outlined,
                validator: _validateLastName,
                onFieldSubmitted: (_) => _emailFocusNode.requestFocus(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        AuthTextField(
          controller: _emailController,
          focusNode: _emailFocusNode,
          labelText: AuthStrings.email,
          hintText: AuthStrings.emailHint,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.email_outlined,
          validator: _validateEmail,
          onFieldSubmitted: (_) => _passwordFocusNode.requestFocus(),
        ),
        const SizedBox(height: 16),
        AuthTextField(
          controller: _passwordController,
          focusNode: _passwordFocusNode,
          labelText: AuthStrings.password,
          hintText: AuthStrings.passwordHint,
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
          ),
          validator: _validatePassword,
          onFieldSubmitted: (_) => _confirmPasswordFocusNode.requestFocus(),
        ),
        const SizedBox(height: 16),
        AuthTextField(
          controller: _confirmPasswordController,
          focusNode: _confirmPasswordFocusNode,
          labelText: AuthStrings.confirmPassword,
          hintText: AuthStrings.confirmPasswordHint,
          obscureText: _obscureConfirmPassword,
          textInputAction: TextInputAction.done,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () => setState(() => _obscureConfirmPassword = !_obscureConfirmPassword),
          ),
          validator: _validateConfirmPassword,
          onFieldSubmitted: (_) => _handleSignUp(),
        ),
      ],
    );
  }

  Widget _buildTermsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _agreeToTerms,
          onChanged: (value) => setState(() => _agreeToTerms = value ?? false),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => setState(() => _agreeToTerms = !_agreeToTerms),
            child: Padding(
              padding: const EdgeInsets.only(top: 12),
              child: RichText(
                text: TextSpan(
                  style: Theme.of(context).textTheme.bodySmall,
                  children: [
                    TextSpan(text: AuthStrings.agreeToTerms),
                    const TextSpan(text: ' '),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => context.push(AuthRoutes.termsOfService),
                        child: Text(
                          AuthStrings.termsOfService,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                    TextSpan(text: ' ${AuthStrings.and} '),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => context.push(AuthRoutes.privacyPolicy),
                        child: Text(
                          AuthStrings.privacyPolicy,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Observer(
      builder: (context) {
        if (!widget.authStore.hasError) {
          return const SizedBox.shrink();
        }
        
        return AuthErrorWidget(
          message: widget.authStore.lastErrorMessage ?? AuthStrings.genericError,
          onDismiss: widget.authStore.clearError,
        );
      },
    );
  }

  Widget _buildSignUpButton() {
    return Observer(
      builder: (context) => AuthButton(
        text: AuthStrings.signUp,
        onPressed: widget.authStore.isLoading || !_agreeToTerms ? null : _handleSignUp,
        isLoading: widget.authStore.isLoading,
      ),
    );
  }

  Widget _buildSignInPrompt() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AuthStrings.alreadyHaveAccount,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(width: 4),
        TextButton(
          onPressed: () => context.pop(),
          child: Text(
            AuthStrings.signIn,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  String? _validateFirstName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AuthStrings.firstNameRequired;
    }
    return null;
  }

  String? _validateLastName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AuthStrings.lastNameRequired;
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AuthStrings.emailRequired;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return AuthStrings.emailInvalid;
    }
    
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AuthStrings.passwordRequired;
    }
    
    if (value.length < 6) {
      return AuthStrings.passwordTooShort;
    }
    
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AuthStrings.passwordRequired;
    }
    
    if (value != _passwordController.text) {
      return AuthStrings.passwordsDoNotMatch;
    }
    
    return null;
  }

  void _handleSignUp() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please agree to the terms and conditions'),
        ),
      );
      return;
    }

    widget.authStore.createUserWithEmailAndPassword(
      email: _emailController.text.trim(),
      password: _passwordController.text,
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
    );
  }
}
