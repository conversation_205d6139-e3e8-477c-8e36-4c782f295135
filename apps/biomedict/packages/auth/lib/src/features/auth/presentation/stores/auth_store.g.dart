// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AuthStore on _AuthStore, Store {
  Computed<bool>? _$isAuthenticatedComputed;

  @override
  bool get isAuthenticated =>
      (_$isAuthenticatedComputed ??= Computed<bool>(
            () => super.isAuthenticated,
            name: '_AuthStore.isAuthenticated',
          ))
          .value;
  Computed<bool>? _$isUnauthenticatedComputed;

  @override
  bool get isUnauthenticated =>
      (_$isUnauthenticatedComputed ??= Computed<bool>(
            () => super.isUnauthenticated,
            name: '_AuthStore.isUnauthenticated',
          ))
          .value;
  Computed<bool>? _$isEmailVerificationRequiredComputed;

  @override
  bool get isEmailVerificationRequired =>
      (_$isEmailVerificationRequiredComputed ??= Computed<bool>(
            () => super.isEmailVerificationRequired,
            name: '_AuthStore.isEmailVerificationRequired',
          ))
          .value;
  Computed<bool>? _$hasErrorComputed;

  @override
  bool get hasError =>
      (_$hasErrorComputed ??= Computed<bool>(
            () => super.hasError,
            name: '_AuthStore.hasError',
          ))
          .value;
  Computed<AuthUser?>? _$currentUserComputed;

  @override
  AuthUser? get currentUser =>
      (_$currentUserComputed ??= Computed<AuthUser?>(
            () => super.currentUser,
            name: '_AuthStore.currentUser',
          ))
          .value;
  Computed<bool>? _$isEmailVerifiedComputed;

  @override
  bool get isEmailVerified =>
      (_$isEmailVerifiedComputed ??= Computed<bool>(
            () => super.isEmailVerified,
            name: '_AuthStore.isEmailVerified',
          ))
          .value;
  Computed<bool>? _$hasCompleteProfileComputed;

  @override
  bool get hasCompleteProfile =>
      (_$hasCompleteProfileComputed ??= Computed<bool>(
            () => super.hasCompleteProfile,
            name: '_AuthStore.hasCompleteProfile',
          ))
          .value;

  late final _$authStateAtom = Atom(
    name: '_AuthStore.authState',
    context: context,
  );

  @override
  AuthState get authState {
    _$authStateAtom.reportRead();
    return super.authState;
  }

  @override
  set authState(AuthState value) {
    _$authStateAtom.reportWrite(value, super.authState, () {
      super.authState = value;
    });
  }

  late final _$lastErrorMessageAtom = Atom(
    name: '_AuthStore.lastErrorMessage',
    context: context,
  );

  @override
  String? get lastErrorMessage {
    _$lastErrorMessageAtom.reportRead();
    return super.lastErrorMessage;
  }

  @override
  set lastErrorMessage(String? value) {
    _$lastErrorMessageAtom.reportWrite(value, super.lastErrorMessage, () {
      super.lastErrorMessage = value;
    });
  }

  late final _$isLoadingAtom = Atom(
    name: '_AuthStore.isLoading',
    context: context,
  );

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$loadingMessageAtom = Atom(
    name: '_AuthStore.loadingMessage',
    context: context,
  );

  @override
  String? get loadingMessage {
    _$loadingMessageAtom.reportRead();
    return super.loadingMessage;
  }

  @override
  set loadingMessage(String? value) {
    _$loadingMessageAtom.reportWrite(value, super.loadingMessage, () {
      super.loadingMessage = value;
    });
  }

  late final _$signInWithEmailAndPasswordAsyncAction = AsyncAction(
    '_AuthStore.signInWithEmailAndPassword',
    context: context,
  );

  @override
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) {
    return _$signInWithEmailAndPasswordAsyncAction.run(
      () => super.signInWithEmailAndPassword(email: email, password: password),
    );
  }

  late final _$createUserWithEmailAndPasswordAsyncAction = AsyncAction(
    '_AuthStore.createUserWithEmailAndPassword',
    context: context,
  );

  @override
  Future<void> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) {
    return _$createUserWithEmailAndPasswordAsyncAction.run(
      () => super.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
      ),
    );
  }

  late final _$signOutAsyncAction = AsyncAction(
    '_AuthStore.signOut',
    context: context,
  );

  @override
  Future<void> signOut() {
    return _$signOutAsyncAction.run(() => super.signOut());
  }

  late final _$sendPasswordResetEmailAsyncAction = AsyncAction(
    '_AuthStore.sendPasswordResetEmail',
    context: context,
  );

  @override
  Future<void> sendPasswordResetEmail({required String email}) {
    return _$sendPasswordResetEmailAsyncAction.run(
      () => super.sendPasswordResetEmail(email: email),
    );
  }

  late final _$sendEmailVerificationAsyncAction = AsyncAction(
    '_AuthStore.sendEmailVerification',
    context: context,
  );

  @override
  Future<void> sendEmailVerification() {
    return _$sendEmailVerificationAsyncAction.run(
      () => super.sendEmailVerification(),
    );
  }

  late final _$reloadUserAsyncAction = AsyncAction(
    '_AuthStore.reloadUser',
    context: context,
  );

  @override
  Future<void> reloadUser() {
    return _$reloadUserAsyncAction.run(() => super.reloadUser());
  }

  late final _$updateProfileAsyncAction = AsyncAction(
    '_AuthStore.updateProfile',
    context: context,
  );

  @override
  Future<void> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
  }) {
    return _$updateProfileAsyncAction.run(
      () => super.updateProfile(
        displayName: displayName,
        firstName: firstName,
        lastName: lastName,
        photoUrl: photoUrl,
      ),
    );
  }

  late final _$_AuthStoreActionController = ActionController(
    name: '_AuthStore',
    context: context,
  );

  @override
  void _initializeAuthState() {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._initializeAuthState',
    );
    try {
      return super._initializeAuthState();
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _handleAuthStateChange(AuthUser? user) {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._handleAuthStateChange',
    );
    try {
      return super._handleAuthStateChange(user);
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearError() {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore.clearError',
    );
    try {
      return super.clearError();
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setLoadingState(String message) {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._setLoadingState',
    );
    try {
      return super._setLoadingState(message);
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _clearLoadingState() {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._clearLoadingState',
    );
    try {
      return super._clearLoadingState();
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setErrorState(AuthException exception) {
    final _$actionInfo = _$_AuthStoreActionController.startAction(
      name: '_AuthStore._setErrorState',
    );
    try {
      return super._setErrorState(exception);
    } finally {
      _$_AuthStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
authState: ${authState},
lastErrorMessage: ${lastErrorMessage},
isLoading: ${isLoading},
loadingMessage: ${loadingMessage},
isAuthenticated: ${isAuthenticated},
isUnauthenticated: ${isUnauthenticated},
isEmailVerificationRequired: ${isEmailVerificationRequired},
hasError: ${hasError},
currentUser: ${currentUser},
isEmailVerified: ${isEmailVerified},
hasCompleteProfile: ${hasCompleteProfile}
    ''';
  }
}
