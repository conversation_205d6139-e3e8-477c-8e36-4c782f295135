import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';

import '../stores/auth_store.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_error_widget.dart';
import '../widgets/auth_loading_overlay.dart';
import '../../../../core/constants/auth_strings.dart';
import '../../../../core/constants/auth_routes.dart';

/// Email verification screen for confirming user email addresses.
/// 
/// This screen guides users through the email verification process
/// and provides options to resend verification emails.
class AuthEmailVerificationScreen extends StatefulWidget {
  final AuthStore authStore;

  const AuthEmailVerificationScreen({
    super.key,
    required this.authStore,
  });

  @override
  State<AuthEmailVerificationScreen> createState() => _AuthEmailVerificationScreenState();
}

class _AuthEmailVerificationScreenState extends State<AuthEmailVerificationScreen> {
  bool _isCheckingVerification = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AuthStrings.emailVerification),
        actions: [
          TextButton(
            onPressed: _handleSignOut,
            child: Text(
              AuthStrings.signOut,
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
      body: Observer(
        builder: (context) => AuthLoadingOverlay(
          isLoading: widget.authStore.isLoading || _isCheckingVerification,
          loadingMessage: widget.authStore.loadingMessage ?? 
              (_isCheckingVerification ? AuthStrings.verifyingEmail : null),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Spacer(),
                  _buildHeader(),
                  const SizedBox(height: 48),
                  _buildEmailInfo(),
                  const SizedBox(height: 32),
                  _buildInstructions(),
                  const SizedBox(height: 32),
                  _buildErrorWidget(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                  const Spacer(flex: 2),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.mark_email_unread_outlined,
          size: 64,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 16),
        Text(
          AuthStrings.emailVerificationRequired,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmailInfo() {
    final user = widget.authStore.currentUser;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.email_outlined,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'Verification email sent to:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            user?.email ?? 'Unknown email',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Next Steps:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            AuthStrings.emailVerificationDescription,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 12),
          _buildInstructionStep('1. Check your email inbox'),
          _buildInstructionStep('2. Look for an email from ${AuthStrings.appName}'),
          _buildInstructionStep('3. Click the verification link'),
          _buildInstructionStep('4. Return here and tap "Check Email & Continue"'),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Observer(
      builder: (context) {
        if (!widget.authStore.hasError) {
          return const SizedBox.shrink();
        }
        
        return AuthErrorWidget(
          message: widget.authStore.lastErrorMessage ?? AuthStrings.genericError,
          onDismiss: widget.authStore.clearError,
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Observer(
          builder: (context) => AuthButton(
            text: AuthStrings.checkEmailAndContinue,
            onPressed: widget.authStore.isLoading || _isCheckingVerification 
                ? null 
                : _handleCheckVerification,
            isLoading: _isCheckingVerification,
            icon: Icons.refresh,
          ),
        ),
        const SizedBox(height: 12),
        Observer(
          builder: (context) => AuthButton(
            text: AuthStrings.resendVerificationEmail,
            onPressed: widget.authStore.isLoading || _isCheckingVerification 
                ? null 
                : _handleResendVerification,
            isLoading: widget.authStore.isLoading,
            isSecondary: true,
            icon: Icons.send,
          ),
        ),
      ],
    );
  }

  void _handleCheckVerification() async {
    setState(() => _isCheckingVerification = true);
    
    try {
      await widget.authStore.reloadUser();
      
      // Check if email is now verified
      if (widget.authStore.isEmailVerified) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AuthStrings.emailVerified),
              backgroundColor: Colors.green,
            ),
          );
          context.go(AuthRoutes.defaultAuthenticatedRoute);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Email not yet verified. Please check your email and try again.'),
            ),
          );
        }
      }
    } catch (e) {
      // Error is handled by the store
    } finally {
      if (mounted) {
        setState(() => _isCheckingVerification = false);
      }
    }
  }

  void _handleResendVerification() async {
    try {
      await widget.authStore.sendEmailVerification();
      
      if (mounted && !widget.authStore.hasError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AuthStrings.emailVerificationSent),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Error is handled by the store
    }
  }

  void _handleSignOut() async {
    try {
      await widget.authStore.signOut();
      if (mounted) {
        context.go(AuthRoutes.signIn);
      }
    } catch (e) {
      // Error is handled by the store
    }
  }
}
