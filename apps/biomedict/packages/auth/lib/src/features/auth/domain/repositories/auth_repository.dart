import '../models/auth_user.dart';
import '../models/auth_exception.dart';

/// Abstract repository interface for authentication operations.
/// 
/// This interface defines the contract for authentication-related operations
/// and can be implemented by different authentication providers (Firebase, etc.).
abstract class AuthRepository {
  /// Stream of authentication state changes
  Stream<AuthUser?> get authStateChanges;

  /// Current authenticated user (if any)
  AuthUser? get currentUser;

  /// Sign in with email and password
  /// 
  /// Throws [AuthException] if sign in fails
  Future<AuthUser> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Create a new user account with email and password
  /// 
  /// Throws [AuthException] if account creation fails
  Future<AuthUser> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  });

  /// Sign out the current user
  /// 
  /// Throws [AuthException] if sign out fails
  Future<void> signOut();

  /// Send password reset email
  /// 
  /// Throws [AuthException] if operation fails
  Future<void> sendPasswordResetEmail({
    required String email,
  });

  /// Send email verification to current user
  /// 
  /// Throws [AuthException] if operation fails
  Future<void> sendEmailVerification();

  /// Reload current user data
  /// 
  /// Throws [AuthException] if operation fails
  Future<void> reloadUser();

  /// Update user profile information
  /// 
  /// Throws [AuthException] if update fails
  Future<AuthUser> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
  });

  /// Update user email address
  /// 
  /// Throws [AuthException] if update fails
  Future<void> updateEmail({
    required String newEmail,
  });

  /// Update user password
  /// 
  /// Throws [AuthException] if update fails
  Future<void> updatePassword({
    required String newPassword,
  });

  /// Delete the current user account
  /// 
  /// Throws [AuthException] if deletion fails
  Future<void> deleteAccount();

  /// Re-authenticate user with current credentials
  /// 
  /// Throws [AuthException] if re-authentication fails
  Future<void> reauthenticateWithCredential({
    required String email,
    required String password,
  });

  /// Check if email is already registered
  /// 
  /// Returns list of sign-in methods for the email
  /// Throws [AuthException] if operation fails
  Future<List<String>> fetchSignInMethodsForEmail({
    required String email,
  });

  /// Verify password reset code
  /// 
  /// Returns the email associated with the code
  /// Throws [AuthException] if verification fails
  Future<String> verifyPasswordResetCode({
    required String code,
  });

  /// Confirm password reset with code and new password
  /// 
  /// Throws [AuthException] if operation fails
  Future<void> confirmPasswordReset({
    required String code,
    required String newPassword,
  });

  /// Apply action code (for email verification, password reset, etc.)
  /// 
  /// Throws [AuthException] if operation fails
  Future<void> applyActionCode({
    required String code,
  });

  /// Check action code (for email verification, password reset, etc.)
  /// 
  /// Returns information about the action code
  /// Throws [AuthException] if operation fails
  Future<Map<String, dynamic>> checkActionCode({
    required String code,
  });
}
