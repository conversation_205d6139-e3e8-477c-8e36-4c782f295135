import 'package:logger/logger.dart';

import '../models/auth_user.dart';
import '../models/auth_exception.dart';
import '../repositories/auth_repository.dart';
import '../../presentation/stores/auth_store.dart';

/// Service layer for authentication operations.
/// 
/// This service provides high-level authentication operations and business logic,
/// acting as an intermediary between the presentation layer and the repository.
class AuthService {
  final AuthRepository _authRepository;
  final AuthStore _authStore;
  final Logger _logger;

  AuthService({
    required AuthRepository authRepository,
    required AuthStore authStore,
    Logger? logger,
  })  : _authRepository = authRepository,
        _authStore = authStore,
        _logger = logger ?? Logger();

  /// Current authenticated user
  AuthUser? get currentUser => _authRepository.currentUser;

  /// Stream of authentication state changes
  Stream<AuthUser?> get authStateChanges => _authRepository.authStateChanges;

  /// Check if user is currently authenticated
  bool get isAuthenticated => _authStore.isAuthenticated;

  /// Check if user's email is verified
  bool get isEmailVerified => _authStore.isEmailVerified;

  /// Sign in with email and password
  Future<AuthUser> signIn({
    required String email,
    required String password,
  }) async {
    _logger.i('AuthService: Attempting sign in for $email');
    
    // Validate input
    _validateEmail(email);
    _validatePassword(password);

    try {
      await _authStore.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = currentUser;
      if (user == null) {
        throw AuthException.generic('Sign in failed: No user returned');
      }

      _logger.i('AuthService: Sign in successful for ${user.email}');
      return user;
    } catch (e) {
      _logger.e('AuthService: Sign in failed - $e');
      rethrow;
    }
  }

  /// Create new user account
  Future<AuthUser> signUp({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) async {
    _logger.i('AuthService: Attempting sign up for $email');
    
    // Validate input
    _validateEmail(email);
    _validatePassword(password);
    if (firstName != null) _validateName(firstName, 'First name');
    if (lastName != null) _validateName(lastName, 'Last name');

    try {
      await _authStore.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
      );

      final user = currentUser;
      if (user == null) {
        throw AuthException.generic('Account creation failed: No user returned');
      }

      _logger.i('AuthService: Sign up successful for ${user.email}');
      return user;
    } catch (e) {
      _logger.e('AuthService: Sign up failed - $e');
      rethrow;
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    _logger.i('AuthService: Signing out user');
    
    try {
      await _authStore.signOut();
      _logger.i('AuthService: Sign out successful');
    } catch (e) {
      _logger.e('AuthService: Sign out failed - $e');
      rethrow;
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    _logger.i('AuthService: Sending password reset email to $email');
    
    _validateEmail(email);

    try {
      await _authStore.sendPasswordResetEmail(email: email);
      _logger.i('AuthService: Password reset email sent successfully');
    } catch (e) {
      _logger.e('AuthService: Password reset email failed - $e');
      rethrow;
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    _logger.i('AuthService: Sending email verification');
    
    if (currentUser == null) {
      throw AuthException.generic('No user is currently signed in');
    }

    try {
      await _authStore.sendEmailVerification();
      _logger.i('AuthService: Email verification sent successfully');
    } catch (e) {
      _logger.e('AuthService: Email verification failed - $e');
      rethrow;
    }
  }

  /// Reload current user data
  Future<void> reloadUser() async {
    _logger.d('AuthService: Reloading user data');
    
    if (currentUser == null) {
      throw AuthException.generic('No user is currently signed in');
    }

    try {
      await _authStore.reloadUser();
      _logger.d('AuthService: User data reloaded successfully');
    } catch (e) {
      _logger.e('AuthService: User reload failed - $e');
      rethrow;
    }
  }

  /// Update user profile
  Future<AuthUser> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
  }) async {
    _logger.i('AuthService: Updating user profile');
    
    if (currentUser == null) {
      throw AuthException.generic('No user is currently signed in');
    }

    // Validate input
    if (firstName != null) _validateName(firstName, 'First name');
    if (lastName != null) _validateName(lastName, 'Last name');

    try {
      await _authStore.updateProfile(
        displayName: displayName,
        firstName: firstName,
        lastName: lastName,
        photoUrl: photoUrl,
      );

      final updatedUser = currentUser;
      if (updatedUser == null) {
        throw AuthException.generic('Profile update failed: No user returned');
      }

      _logger.i('AuthService: Profile updated successfully');
      return updatedUser;
    } catch (e) {
      _logger.e('AuthService: Profile update failed - $e');
      rethrow;
    }
  }

  /// Check if email is already registered
  Future<bool> isEmailRegistered({
    required String email,
  }) async {
    _logger.d('AuthService: Checking if email is registered: $email');
    
    _validateEmail(email);

    try {
      final methods = await _authRepository.fetchSignInMethodsForEmail(email: email);
      final isRegistered = methods.isNotEmpty;
      _logger.d('AuthService: Email registration check result: $isRegistered');
      return isRegistered;
    } catch (e) {
      _logger.e('AuthService: Email registration check failed - $e');
      rethrow;
    }
  }

  /// Clear authentication errors
  void clearError() {
    _authStore.clearError();
  }

  /// Validate email format
  void _validateEmail(String email) {
    if (email.isEmpty) {
      throw AuthException(
        message: 'Email is required',
        code: 'email-required',
      );
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      throw AuthException(
        message: 'Please enter a valid email address',
        code: 'invalid-email-format',
      );
    }
  }

  /// Validate password strength
  void _validatePassword(String password) {
    if (password.isEmpty) {
      throw AuthException(
        message: 'Password is required',
        code: 'password-required',
      );
    }

    if (password.length < 6) {
      throw AuthException(
        message: 'Password must be at least 6 characters',
        code: 'password-too-short',
      );
    }
  }

  /// Validate name fields
  void _validateName(String name, String fieldName) {
    if (name.trim().isEmpty) {
      throw AuthException(
        message: '$fieldName is required',
        code: 'name-required',
      );
    }

    if (name.trim().length < 2) {
      throw AuthException(
        message: '$fieldName must be at least 2 characters',
        code: 'name-too-short',
      );
    }
  }

  /// Dispose service resources
  void dispose() {
    _logger.d('AuthService: Disposing resources');
    _authStore.dispose();
  }
}
