import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../domain/models/auth_state.dart';
import '../stores/auth_store.dart';
import '../../../../core/constants/auth_routes.dart';

/// Route guard for protecting authenticated routes.
/// 
/// This guard checks authentication state and redirects users to appropriate
/// screens based on their authentication status and email verification state.
class AuthGuard {
  final AuthStore _authStore;
  final Logger _logger;

  AuthGuard({
    required AuthStore authStore,
    Logger? logger,
  })  : _authStore = authStore,
        _logger = logger ?? Logger();

  /// Redirect logic for protected routes
  String? redirectLogic(BuildContext context, GoRouterState state) {
    final currentLocation = state.uri.toString();
    final authState = _authStore.authState;
    
    _logger.d('AuthGuard: Checking route access for $currentLocation');
    _logger.d('AuthGuard: Current auth state: ${authState.runtimeType}');

    // Handle different authentication states
    switch (authState.runtimeType) {
      case AuthStateInitial:
      case AuthStateLoading:
        // Still loading, allow navigation but the UI will show loading state
        _logger.d('AuthGuard: Auth state loading, allowing navigation');
        return null;

      case AuthStateUnauthenticated:
        return _handleUnauthenticatedUser(currentLocation);

      case AuthStateEmailVerificationRequired:
        return _handleEmailVerificationRequired(currentLocation);

      case AuthStateAuthenticated:
        return _handleAuthenticatedUser(currentLocation);

      case AuthStateError:
        return _handleAuthError(currentLocation);

      default:
        _logger.w('AuthGuard: Unknown auth state: ${authState.runtimeType}');
        return AuthRoutes.signIn;
    }
  }

  /// Handle unauthenticated user access
  String? _handleUnauthenticatedUser(String currentLocation) {
    _logger.d('AuthGuard: User is unauthenticated');

    // Allow access to public routes
    if (AuthRoutes.isPublicRoute(currentLocation) || 
        AuthRoutes.isAuthRoute(currentLocation)) {
      _logger.d('AuthGuard: Allowing access to public/auth route');
      return null;
    }

    // Redirect to sign in for protected routes
    _logger.d('AuthGuard: Redirecting to sign in');
    return AuthRoutes.signIn;
  }

  /// Handle email verification required state
  String? _handleEmailVerificationRequired(String currentLocation) {
    _logger.d('AuthGuard: Email verification required');

    // Allow access to email verification route
    if (currentLocation == AuthRoutes.emailVerification) {
      return null;
    }

    // Allow access to auth routes (user might want to sign out)
    if (AuthRoutes.isAuthRoute(currentLocation)) {
      return null;
    }

    // Redirect to email verification for all other routes
    _logger.d('AuthGuard: Redirecting to email verification');
    return AuthRoutes.emailVerification;
  }

  /// Handle authenticated user access
  String? _handleAuthenticatedUser(String currentLocation) {
    _logger.d('AuthGuard: User is authenticated');

    // Redirect away from auth routes to dashboard
    if (AuthRoutes.isAuthRoute(currentLocation)) {
      _logger.d('AuthGuard: Redirecting authenticated user to dashboard');
      return AuthRoutes.defaultAuthenticatedRoute;
    }

    // Allow access to all other routes
    return null;
  }

  /// Handle authentication error state
  String? _handleAuthError(String currentLocation) {
    _logger.w('AuthGuard: Auth error state detected');

    // Allow access to auth routes so user can try to sign in again
    if (AuthRoutes.isAuthRoute(currentLocation) || 
        AuthRoutes.isPublicRoute(currentLocation)) {
      return null;
    }

    // Redirect to sign in for protected routes
    _logger.d('AuthGuard: Redirecting to sign in due to auth error');
    return AuthRoutes.signIn;
  }

  /// Check if a route requires authentication
  bool requiresAuth(String route) {
    return AuthRoutes.isProtectedRoute(route);
  }

  /// Check if user can access a specific route
  bool canAccess(String route) {
    final authState = _authStore.authState;

    // Public routes are always accessible
    if (AuthRoutes.isPublicRoute(route)) {
      return true;
    }

    // Auth routes are accessible when not authenticated or in error state
    if (AuthRoutes.isAuthRoute(route)) {
      return authState.isUnauthenticated || 
             authState.isError || 
             authState.isEmailVerificationRequired;
    }

    // Protected routes require authentication and email verification
    if (AuthRoutes.isProtectedRoute(route)) {
      return authState.isAuthenticated;
    }

    // Default to allowing access
    return true;
  }

  /// Get the appropriate redirect route for current auth state
  String getRedirectRoute() {
    final authState = _authStore.authState;

    switch (authState.runtimeType) {
      case AuthStateUnauthenticated:
      case AuthStateError:
        return AuthRoutes.defaultUnauthenticatedRoute;

      case AuthStateEmailVerificationRequired:
        return AuthRoutes.emailVerification;

      case AuthStateAuthenticated:
        return AuthRoutes.defaultAuthenticatedRoute;

      default:
        return AuthRoutes.defaultUnauthenticatedRoute;
    }
  }

  /// Create a GoRouter redirect function
  String? Function(BuildContext, GoRouterState) get redirect => redirectLogic;
}

/// Extension to add auth guard functionality to GoRouter
extension GoRouterAuthExtension on GoRouter {
  /// Navigate with authentication check
  void goWithAuth(String location, {Object? extra}) {
    // The redirect logic in the router configuration will handle auth checks
    go(location, extra: extra);
  }

  /// Push with authentication check
  void pushWithAuth(String location, {Object? extra}) {
    // The redirect logic in the router configuration will handle auth checks
    push(location, extra: extra);
  }
}
