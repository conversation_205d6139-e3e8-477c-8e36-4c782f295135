import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';

import '../../domain/models/auth_user.dart';
import '../../domain/models/auth_exception.dart';
import '../../domain/repositories/auth_repository.dart';
import '../mappers/auth_user_mapper.dart';

/// Firebase implementation of the AuthRepository interface.
/// 
/// This class handles all Firebase Authentication operations and maps
/// Firebase User objects to our domain AuthUser model.
class FirebaseAuthRepository implements AuthRepository {
  final FirebaseAuth _firebaseAuth;
  final Logger _logger;
  final AuthUserMapper _userMapper;

  FirebaseAuthRepository({
    FirebaseAuth? firebaseAuth,
    Logger? logger,
    AuthUserMapper? userMapper,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _logger = logger ?? Logger(),
        _userMapper = userMapper ?? const AuthUserMapper();

  @override
  Stream<AuthUser?> get authStateChanges {
    return _firebaseAuth.authStateChanges().map((user) {
      if (user == null) return null;
      return _userMapper.fromFirebaseUser(user);
    });
  }

  @override
  AuthUser? get currentUser {
    final user = _firebaseAuth.currentUser;
    if (user == null) return null;
    return _userMapper.fromFirebaseUser(user);
  }

  @override
  Future<AuthUser> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign in user with email: $email');
      
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        throw AuthException.generic('Sign in failed: No user returned');
      }

      final authUser = _userMapper.fromFirebaseUser(credential.user!);
      _logger.i('Successfully signed in user: ${authUser.email}');
      
      return authUser;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase sign in error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected sign in error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred during sign in');
    }
  }

  @override
  Future<AuthUser> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) async {
    try {
      _logger.i('Attempting to create user with email: $email');
      
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        throw AuthException.generic('Account creation failed: No user returned');
      }

      // Update display name if first and last names are provided
      if (firstName != null || lastName != null) {
        final displayName = [firstName, lastName]
            .where((name) => name != null && name.isNotEmpty)
            .join(' ');
        
        if (displayName.isNotEmpty) {
          await credential.user!.updateDisplayName(displayName);
          await credential.user!.reload();
        }
      }

      // Send email verification
      await credential.user!.sendEmailVerification();
      _logger.i('Email verification sent to: $email');

      final authUser = _userMapper.fromFirebaseUser(credential.user!);
      _logger.i('Successfully created user: ${authUser.email}');
      
      return authUser;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase account creation error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected account creation error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred during account creation');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      _logger.i('Signing out current user');
      await _firebaseAuth.signOut();
      _logger.i('Successfully signed out user');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase sign out error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected sign out error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred during sign out');
    }
  }

  @override
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      _logger.i('Sending password reset email to: $email');
      await _firebaseAuth.sendPasswordResetEmail(email: email.trim());
      _logger.i('Successfully sent password reset email');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase password reset error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected password reset error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while sending password reset email');
    }
  }

  @override
  Future<void> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.i('Sending email verification to: ${user.email}');
      await user.sendEmailVerification();
      _logger.i('Successfully sent email verification');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase email verification error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected email verification error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while sending email verification');
    }
  }

  @override
  Future<void> reloadUser() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.d('Reloading user data');
      await user.reload();
      _logger.d('Successfully reloaded user data');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase user reload error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected user reload error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while reloading user data');
    }
  }

  @override
  Future<AuthUser> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.i('Updating user profile');

      // Update display name if provided or construct from first/last name
      String? newDisplayName = displayName;
      if (newDisplayName == null && (firstName != null || lastName != null)) {
        newDisplayName = [firstName, lastName]
            .where((name) => name != null && name.isNotEmpty)
            .join(' ');
      }

      if (newDisplayName != null || photoUrl != null) {
        await user.updateDisplayName(newDisplayName);
        if (photoUrl != null) {
          await user.updatePhotoURL(photoUrl);
        }
        await user.reload();
      }

      final updatedUser = _userMapper.fromFirebaseUser(user);
      _logger.i('Successfully updated user profile');

      return updatedUser;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase profile update error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected profile update error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while updating profile');
    }
  }

  @override
  Future<void> updateEmail({
    required String newEmail,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.i('Updating user email to: $newEmail');
      await user.verifyBeforeUpdateEmail(newEmail.trim());
      _logger.i('Email update verification sent');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase email update error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected email update error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while updating email');
    }
  }

  @override
  Future<void> updatePassword({
    required String newPassword,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.i('Updating user password');
      await user.updatePassword(newPassword);
      _logger.i('Successfully updated password');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase password update error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected password update error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while updating password');
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.i('Deleting user account: ${user.email}');
      await user.delete();
      _logger.i('Successfully deleted user account');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase account deletion error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected account deletion error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while deleting account');
    }
  }

  @override
  Future<void> reauthenticateWithCredential({
    required String email,
    required String password,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw AuthException.generic('No user is currently signed in');
      }

      _logger.i('Re-authenticating user');
      final credential = EmailAuthProvider.credential(
        email: email.trim(),
        password: password,
      );

      await user.reauthenticateWithCredential(credential);
      _logger.i('Successfully re-authenticated user');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase re-authentication error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected re-authentication error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred during re-authentication');
    }
  }

  @override
  Future<List<String>> fetchSignInMethodsForEmail({
    required String email,
  }) async {
    try {
      _logger.d('Fetching sign-in methods for email: $email');
      final methods = await _firebaseAuth.fetchSignInMethodsForEmail(email.trim());
      _logger.d('Found ${methods.length} sign-in methods');
      return methods;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase fetch sign-in methods error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected fetch sign-in methods error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while checking email');
    }
  }

  @override
  Future<String> verifyPasswordResetCode({
    required String code,
  }) async {
    try {
      _logger.d('Verifying password reset code');
      final email = await _firebaseAuth.verifyPasswordResetCode(code);
      _logger.d('Password reset code verified for email: $email');
      return email;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase password reset code verification error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected password reset code verification error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while verifying reset code');
    }
  }

  @override
  Future<void> confirmPasswordReset({
    required String code,
    required String newPassword,
  }) async {
    try {
      _logger.i('Confirming password reset');
      await _firebaseAuth.confirmPasswordReset(
        code: code,
        newPassword: newPassword,
      );
      _logger.i('Successfully reset password');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase password reset confirmation error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected password reset confirmation error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while resetting password');
    }
  }

  @override
  Future<void> applyActionCode({
    required String code,
  }) async {
    try {
      _logger.d('Applying action code');
      await _firebaseAuth.applyActionCode(code);
      _logger.d('Successfully applied action code');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase apply action code error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected apply action code error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while applying action code');
    }
  }

  @override
  Future<Map<String, dynamic>> checkActionCode({
    required String code,
  }) async {
    try {
      _logger.d('Checking action code');
      final info = await _firebaseAuth.checkActionCode(code);

      final result = {
        'operation': info.operation.toString(),
        'email': info.data['email'],
        'previousEmail': info.data['previousEmail'],
      };

      _logger.d('Action code checked successfully');
      return result;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase check action code error: ${e.code} - ${e.message}');
      throw AuthException.fromFirebaseAuthCode(e.code, e);
    } catch (e, stackTrace) {
      _logger.e('Unexpected check action code error: $e', error: e, stackTrace: stackTrace);
      throw AuthException.generic('An unexpected error occurred while checking action code');
    }
  }
}
