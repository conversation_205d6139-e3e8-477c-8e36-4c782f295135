import 'dart:async';
import 'package:mobx/mobx.dart';
import 'package:logger/logger.dart';

import '../../domain/models/auth_user.dart';
import '../../domain/models/auth_state.dart';
import '../../domain/models/auth_exception.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../../../core/constants/auth_strings.dart';

part 'auth_store.g.dart';

/// MobX store for managing authentication state and operations.
/// 
/// This store provides reactive state management for authentication
/// throughout the application using MobX observables and actions.
class AuthStore = _AuthStore with _$AuthStore;

abstract class _AuthStore with Store {
  final AuthRepository _authRepository;
  final Logger _logger;
  StreamSubscription<AuthUser?>? _authStateSubscription;

  _AuthStore({
    required AuthRepository authRepository,
    Logger? logger,
  })  : _authRepository = authRepository,
        _logger = logger ?? Logger() {
    _initializeAuthState();
  }

  // Observable state
  @observable
  AuthState authState = const AuthStateInitial();

  @observable
  String? lastErrorMessage;

  @observable
  bool isLoading = false;

  @observable
  String? loadingMessage;

  // Computed values
  @computed
  bool get isAuthenticated => authState.isAuthenticated;

  @computed
  bool get isUnauthenticated => authState.isUnauthenticated;

  @computed
  bool get isEmailVerificationRequired => authState.isEmailVerificationRequired;

  @computed
  bool get hasError => authState.isError;

  @computed
  AuthUser? get currentUser => authState.user;

  @computed
  bool get isEmailVerified => currentUser?.emailVerified ?? false;

  @computed
  bool get hasCompleteProfile => currentUser?.hasCompleteProfile ?? false;

  // Actions
  @action
  void _initializeAuthState() {
    _logger.d('Initializing auth state');
    authState = const AuthStateLoading();
    
    _authStateSubscription = _authRepository.authStateChanges.listen(
      (user) {
        _logger.d('Auth state changed: ${user?.email ?? 'null'}');
        _handleAuthStateChange(user);
      },
      onError: (error) {
        _logger.e('Auth state stream error: $error');
        _setErrorState(AuthException.generic('Authentication state error'));
      },
    );
  }

  @action
  void _handleAuthStateChange(AuthUser? user) {
    if (user == null) {
      authState = const AuthStateUnauthenticated();
      lastErrorMessage = null;
    } else if (!user.emailVerified) {
      authState = AuthStateEmailVerificationRequired(user: user);
    } else {
      authState = AuthStateAuthenticated(user: user);
      lastErrorMessage = null;
    }
    isLoading = false;
    loadingMessage = null;
  }

  @action
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setLoadingState(AuthStrings.signingIn);
      
      final user = await _authRepository.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      _logger.i('User signed in successfully: ${user.email}');
    } on AuthException catch (e) {
      _logger.e('Sign in failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected sign in error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  Future<void> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? firstName,
    String? lastName,
  }) async {
    try {
      _setLoadingState(AuthStrings.signingUp);
      
      final user = await _authRepository.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
      );
      
      _logger.i('User account created successfully: ${user.email}');
    } on AuthException catch (e) {
      _logger.e('Account creation failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected account creation error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  Future<void> signOut() async {
    try {
      _setLoadingState(AuthStrings.signingOut);
      
      await _authRepository.signOut();
      _logger.i('User signed out successfully');
    } on AuthException catch (e) {
      _logger.e('Sign out failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected sign out error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      _setLoadingState(AuthStrings.sendingResetEmail);
      
      await _authRepository.sendPasswordResetEmail(email: email);
      _logger.i('Password reset email sent to: $email');
      
      _clearLoadingState();
    } on AuthException catch (e) {
      _logger.e('Password reset failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected password reset error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  Future<void> sendEmailVerification() async {
    try {
      _setLoadingState(AuthStrings.verifyingEmail);
      
      await _authRepository.sendEmailVerification();
      _logger.i('Email verification sent');
      
      _clearLoadingState();
    } on AuthException catch (e) {
      _logger.e('Email verification failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected email verification error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  Future<void> reloadUser() async {
    try {
      await _authRepository.reloadUser();
      _logger.d('User data reloaded');
    } on AuthException catch (e) {
      _logger.e('User reload failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected user reload error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  Future<void> updateProfile({
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
  }) async {
    try {
      _setLoadingState('Updating profile...');
      
      await _authRepository.updateProfile(
        displayName: displayName,
        firstName: firstName,
        lastName: lastName,
        photoUrl: photoUrl,
      );
      
      _logger.i('Profile updated successfully');
      _clearLoadingState();
    } on AuthException catch (e) {
      _logger.e('Profile update failed: ${e.message}');
      _setErrorState(e);
    } catch (e) {
      _logger.e('Unexpected profile update error: $e');
      _setErrorState(AuthException.generic());
    }
  }

  @action
  void clearError() {
    lastErrorMessage = null;
    if (authState.isError) {
      authState = const AuthStateUnauthenticated();
    }
  }

  @action
  void _setLoadingState(String message) {
    isLoading = true;
    loadingMessage = message;
    lastErrorMessage = null;
  }

  @action
  void _clearLoadingState() {
    isLoading = false;
    loadingMessage = null;
  }

  @action
  void _setErrorState(AuthException exception) {
    authState = AuthStateError(
      message: exception.message,
      code: exception.code,
      error: exception,
    );
    lastErrorMessage = exception.message;
    isLoading = false;
    loadingMessage = null;
  }

  /// Dispose method to clean up resources
  void dispose() {
    _authStateSubscription?.cancel();
    _logger.d('AuthStore disposed');
  }
}
