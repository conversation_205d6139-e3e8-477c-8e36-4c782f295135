import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/models/auth_user.dart';

/// Mapper class to convert between Firebase User and domain AuthUser models.
/// 
/// This class handles the conversion between Firebase's User object and our
/// domain-specific AuthUser model, ensuring proper data mapping and type safety.
class AuthUserMapper {
  const AuthUserMapper();

  /// Converts a Firebase User to an AuthUser domain model
  AuthUser fromFirebaseUser(User firebaseUser) {
    // Parse display name to extract first and last names if possible
    String? firstName;
    String? lastName;
    
    if (firebaseUser.displayName != null && firebaseUser.displayName!.isNotEmpty) {
      final nameParts = firebaseUser.displayName!.trim().split(' ');
      if (nameParts.isNotEmpty) {
        firstName = nameParts.first;
        if (nameParts.length > 1) {
          lastName = nameParts.skip(1).join(' ');
        }
      }
    }

    // Extract provider IDs
    final providerIds = firebaseUser.providerData
        .map((info) => info.providerId)
        .toList();

    // Create metadata map with additional user information
    final metadata = <String, dynamic>{
      'creationTime': firebaseUser.metadata.creationTime?.toIso8601String(),
      'lastSignInTime': firebaseUser.metadata.lastSignInTime?.toIso8601String(),
      'providerData': firebaseUser.providerData.map((info) => {
        'providerId': info.providerId,
        'uid': info.uid,
        'displayName': info.displayName,
        'email': info.email,
        'phoneNumber': info.phoneNumber,
        'photoURL': info.photoURL,
      }).toList(),
    };

    return AuthUser(
      uid: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      displayName: firebaseUser.displayName,
      firstName: firstName,
      lastName: lastName,
      photoUrl: firebaseUser.photoURL,
      emailVerified: firebaseUser.emailVerified,
      isAnonymous: firebaseUser.isAnonymous,
      providerIds: providerIds,
      phoneNumber: firebaseUser.phoneNumber,
      createdAt: firebaseUser.metadata.creationTime,
      lastSignInAt: firebaseUser.metadata.lastSignInTime,
      metadata: metadata,
    );
  }

  /// Converts an AuthUser back to a map that could be used to update Firebase User
  /// Note: This doesn't create a Firebase User object as that's managed by Firebase
  Map<String, dynamic> toFirebaseUserUpdateMap(AuthUser authUser) {
    String? displayName;
    
    // Construct display name from first and last name if available
    if (authUser.firstName != null || authUser.lastName != null) {
      displayName = [authUser.firstName, authUser.lastName]
          .where((name) => name != null && name.isNotEmpty)
          .join(' ');
    } else {
      displayName = authUser.displayName;
    }

    return {
      'displayName': displayName,
      'photoURL': authUser.photoUrl,
    };
  }

  /// Creates an AuthUser from JSON data (useful for caching or testing)
  AuthUser fromJson(Map<String, dynamic> json) {
    return AuthUser.fromJson(json);
  }

  /// Converts an AuthUser to JSON (useful for caching or serialization)
  Map<String, dynamic> toJson(AuthUser authUser) {
    return authUser.toJson();
  }

  /// Creates a minimal AuthUser for testing purposes
  AuthUser createTestUser({
    String uid = 'test-uid',
    String email = '<EMAIL>',
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
    bool emailVerified = true,
    bool isAnonymous = false,
    List<String> providerIds = const ['password'],
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    Map<String, dynamic>? metadata,
  }) {
    return AuthUser(
      uid: uid,
      email: email,
      displayName: displayName,
      firstName: firstName,
      lastName: lastName,
      photoUrl: photoUrl,
      emailVerified: emailVerified,
      isAnonymous: isAnonymous,
      providerIds: providerIds,
      phoneNumber: phoneNumber,
      createdAt: createdAt ?? DateTime.now(),
      lastSignInAt: lastSignInAt ?? DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Merges user data from multiple sources (useful for profile updates)
  AuthUser mergeUserData(AuthUser baseUser, Map<String, dynamic> updates) {
    return baseUser.copyWith(
      displayName: updates['displayName'] ?? baseUser.displayName,
      firstName: updates['firstName'] ?? baseUser.firstName,
      lastName: updates['lastName'] ?? baseUser.lastName,
      photoUrl: updates['photoUrl'] ?? baseUser.photoUrl,
      phoneNumber: updates['phoneNumber'] ?? baseUser.phoneNumber,
      metadata: updates['metadata'] != null 
          ? {...?baseUser.metadata, ...updates['metadata']}
          : baseUser.metadata,
    );
  }
}
