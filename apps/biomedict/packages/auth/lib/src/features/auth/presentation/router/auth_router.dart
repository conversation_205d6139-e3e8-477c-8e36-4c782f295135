import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../screens/sign_in_screen.dart';
import '../screens/sign_up_screen.dart';
import '../screens/forgot_password_screen.dart';
import '../screens/email_verification_screen.dart';
import '../screens/terms_of_service_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../stores/auth_store.dart';
import '../guards/auth_guard.dart';
import '../../../core/constants/auth_routes.dart';
import '../../di/auth_di.dart';

/// Router configuration for authentication-related routes.
/// 
/// This class provides route definitions and configuration for all
/// authentication screens and flows in the application.
class AuthRouter {
  static final AuthStore _authStore = AuthDI.authStore;
  static final AuthGuard _authGuard = AuthDI.authGuard;

  /// Get all authentication routes
  static List<RouteBase> get routes => [
    // Authentication routes
    GoRoute(
      path: AuthRoutes.signIn,
      name: AuthRoutes.signInName,
      builder: (context, state) => AuthSignInScreen(
        authStore: _authStore,
      ),
    ),
    GoRoute(
      path: AuthRoutes.signUp,
      name: AuthRoutes.signUpName,
      builder: (context, state) => AuthSignUpScreen(
        authStore: _authStore,
      ),
    ),
    GoRoute(
      path: AuthRoutes.forgotPassword,
      name: AuthRoutes.forgotPasswordName,
      builder: (context, state) => AuthForgotPasswordScreen(
        authStore: _authStore,
      ),
    ),
    GoRoute(
      path: AuthRoutes.emailVerification,
      name: AuthRoutes.emailVerificationName,
      builder: (context, state) => AuthEmailVerificationScreen(
        authStore: _authStore,
      ),
    ),
    
    // Legal routes
    GoRoute(
      path: AuthRoutes.termsOfService,
      name: AuthRoutes.termsOfServiceName,
      builder: (context, state) => const AuthTermsOfServiceScreen(),
    ),
    GoRoute(
      path: AuthRoutes.privacyPolicy,
      name: AuthRoutes.privacyPolicyName,
      builder: (context, state) => const AuthPrivacyPolicyScreen(),
    ),
  ];

  /// Create a GoRouter instance with authentication routes and guards
  static GoRouter createRouter({
    List<RouteBase>? additionalRoutes,
    String? initialLocation,
    GlobalKey<NavigatorState>? navigatorKey,
    List<NavigatorObserver>? observers,
    String Function(BuildContext, GoRouterState)? redirect,
    Widget Function(BuildContext, GoRouterState)? errorBuilder,
  }) {
    return GoRouter(
      navigatorKey: navigatorKey,
      observers: observers ?? [],
      initialLocation: initialLocation ?? AuthRoutes.signIn,
      redirect: (context, state) {
        // Apply auth guard redirect logic first
        final authRedirect = _authGuard.redirectLogic(context, state);
        if (authRedirect != null) {
          return authRedirect;
        }
        
        // Apply custom redirect logic if provided
        return redirect?.call(context, state);
      },
      routes: [
        ...routes,
        ...?additionalRoutes,
      ],
      errorBuilder: errorBuilder ?? _defaultErrorBuilder,
    );
  }

  /// Default error builder for routing errors
  static Widget _defaultErrorBuilder(BuildContext context, GoRouterState state) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AuthRoutes.signIn),
              child: const Text('Go to Sign In'),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to sign in screen
  static void goToSignIn(BuildContext context) {
    context.go(AuthRoutes.signIn);
  }

  /// Navigate to sign up screen
  static void goToSignUp(BuildContext context) {
    context.go(AuthRoutes.signUp);
  }

  /// Navigate to forgot password screen
  static void goToForgotPassword(BuildContext context) {
    context.go(AuthRoutes.forgotPassword);
  }

  /// Navigate to email verification screen
  static void goToEmailVerification(BuildContext context) {
    context.go(AuthRoutes.emailVerification);
  }

  /// Navigate to terms of service screen
  static void goToTermsOfService(BuildContext context) {
    context.push(AuthRoutes.termsOfService);
  }

  /// Navigate to privacy policy screen
  static void goToPrivacyPolicy(BuildContext context) {
    context.push(AuthRoutes.privacyPolicy);
  }

  /// Navigate back with fallback to sign in
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      context.go(AuthRoutes.signIn);
    }
  }

  /// Navigate to the appropriate screen based on auth state
  static void navigateBasedOnAuthState(BuildContext context) {
    final authState = _authStore.authState;
    
    if (authState.isAuthenticated) {
      context.go(AuthRoutes.defaultAuthenticatedRoute);
    } else if (authState.isEmailVerificationRequired) {
      context.go(AuthRoutes.emailVerification);
    } else {
      context.go(AuthRoutes.defaultUnauthenticatedRoute);
    }
  }

  /// Check if current route is an auth route
  static bool isAuthRoute(String route) {
    return AuthRoutes.isAuthRoute(route);
  }

  /// Check if current route requires authentication
  static bool requiresAuth(String route) {
    return AuthRoutes.isProtectedRoute(route);
  }

  /// Get the current route name from GoRouterState
  static String? getCurrentRouteName(GoRouterState state) {
    return state.name;
  }

  /// Get the current route path from GoRouterState
  static String getCurrentRoutePath(GoRouterState state) {
    return state.uri.toString();
  }
}

/// Extension to add auth-specific navigation methods to BuildContext
extension AuthNavigationExtension on BuildContext {
  /// Navigate to sign in screen
  void goToSignIn() => AuthRouter.goToSignIn(this);

  /// Navigate to sign up screen
  void goToSignUp() => AuthRouter.goToSignUp(this);

  /// Navigate to forgot password screen
  void goToForgotPassword() => AuthRouter.goToForgotPassword(this);

  /// Navigate to email verification screen
  void goToEmailVerification() => AuthRouter.goToEmailVerification(this);

  /// Navigate to terms of service screen
  void goToTermsOfService() => AuthRouter.goToTermsOfService(this);

  /// Navigate to privacy policy screen
  void goToPrivacyPolicy() => AuthRouter.goToPrivacyPolicy(this);

  /// Navigate back with fallback
  void goBackSafely() => AuthRouter.goBack(this);

  /// Navigate based on current auth state
  void navigateByAuthState() => AuthRouter.navigateBasedOnAuthState(this);
}
