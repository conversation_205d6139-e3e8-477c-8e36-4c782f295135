import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/auth_strings.dart';

/// Terms of Service screen displaying the application's terms and conditions.
/// 
/// This screen provides a placeholder for the application's terms of service
/// with proper Material Design styling and navigation.
class AuthTermsOfServiceScreen extends StatelessWidget {
  const AuthTermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AuthStrings.termsOfService),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 24),
              _buildLastUpdated(context),
              const SizedBox(height: 32),
              _buildSection(
                context,
                'Acceptance of Terms',
                'By accessing and using ${AuthStrings.appName}, you accept and agree to be bound by the terms and provision of this agreement.',
              ),
              _buildSection(
                context,
                'Use License',
                'Permission is granted to temporarily download one copy of ${AuthStrings.appName} for personal, non-commercial transitory viewing only.',
              ),
              _buildSection(
                context,
                'Disclaimer',
                'The materials on ${AuthStrings.appName} are provided on an \'as is\' basis. ${AuthStrings.appName} makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.',
              ),
              _buildSection(
                context,
                'Limitations',
                'In no event shall ${AuthStrings.appName} or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on ${AuthStrings.appName}, even if ${AuthStrings.appName} or an authorized representative has been notified orally or in writing of the possibility of such damage.',
              ),
              _buildSection(
                context,
                'Privacy Policy',
                'Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.',
              ),
              _buildSection(
                context,
                'User Accounts',
                'When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.',
              ),
              _buildSection(
                context,
                'Prohibited Uses',
                'You may not use our service: (a) for any unlawful purpose or to solicit others to perform unlawful acts; (b) to violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances; (c) to infringe upon or violate our intellectual property rights or the intellectual property rights of others.',
              ),
              _buildSection(
                context,
                'Termination',
                'We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.',
              ),
              _buildSection(
                context,
                'Changes to Terms',
                'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.',
              ),
              _buildSection(
                context,
                'Contact Information',
                'If you have any questions about these Terms of Service, please contact us through the app or visit our website.',
              ),
              const SizedBox(height: 32),
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AuthStrings.termsOfService,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Please read these terms and conditions carefully before using ${AuthStrings.appName}.',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildLastUpdated(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.update,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            'Last updated: January 1, 2024',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Important Notice',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'These terms constitute a legally binding agreement between you and ${AuthStrings.appName}. By using our service, you acknowledge that you have read, understood, and agree to be bound by these terms.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
