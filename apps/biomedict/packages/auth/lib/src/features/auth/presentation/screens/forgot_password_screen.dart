import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';

import '../stores/auth_store.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_error_widget.dart';
import '../widgets/auth_loading_overlay.dart';
import '../../../core/constants/auth_strings.dart';

/// Forgot password screen for password reset functionality.
/// 
/// This screen allows users to request a password reset email
/// with proper validation and feedback.
class AuthForgotPasswordScreen extends StatefulWidget {
  final AuthStore authStore;

  const AuthForgotPasswordScreen({
    super.key,
    required this.authStore,
  });

  @override
  State<AuthForgotPasswordScreen> createState() => _AuthForgotPasswordScreenState();
}

class _AuthForgotPasswordScreenState extends State<AuthForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _emailFocusNode = FocusNode();
  
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AuthStrings.resetPassword),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Observer(
        builder: (context) => AuthLoadingOverlay(
          isLoading: widget.authStore.isLoading,
          loadingMessage: widget.authStore.loadingMessage,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: _emailSent ? _buildSuccessView() : _buildResetForm(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResetForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Spacer(),
          _buildHeader(),
          const SizedBox(height: 48),
          _buildEmailField(),
          const SizedBox(height: 24),
          _buildErrorWidget(),
          const SizedBox(height: 24),
          _buildSendButton(),
          const SizedBox(height: 16),
          _buildBackToSignInButton(),
          const Spacer(flex: 2),
        ],
      ),
    );
  }

  Widget _buildSuccessView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Spacer(),
        _buildSuccessHeader(),
        const SizedBox(height: 48),
        _buildSuccessMessage(),
        const SizedBox(height: 32),
        _buildResendButton(),
        const SizedBox(height: 16),
        _buildBackToSignInButton(),
        const Spacer(flex: 2),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.lock_reset,
          size: 64,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 16),
        Text(
          AuthStrings.passwordResetTitle,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          AuthStrings.passwordResetDescription,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuccessHeader() {
    return Column(
      children: [
        Icon(
          Icons.mark_email_read_outlined,
          size: 64,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 16),
        Text(
          AuthStrings.passwordResetEmailSent,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    return AuthTextField(
      controller: _emailController,
      focusNode: _emailFocusNode,
      labelText: AuthStrings.email,
      hintText: AuthStrings.emailHint,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.done,
      prefixIcon: Icons.email_outlined,
      validator: _validateEmail,
      onFieldSubmitted: (_) => _handleSendResetEmail(),
    );
  }

  Widget _buildSuccessMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            AuthStrings.passwordResetEmailDescription,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _emailController.text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Observer(
      builder: (context) {
        if (!widget.authStore.hasError) {
          return const SizedBox.shrink();
        }
        
        return AuthErrorWidget(
          message: widget.authStore.lastErrorMessage ?? AuthStrings.genericError,
          onDismiss: widget.authStore.clearError,
        );
      },
    );
  }

  Widget _buildSendButton() {
    return Observer(
      builder: (context) => AuthButton(
        text: AuthStrings.sendResetEmail,
        onPressed: widget.authStore.isLoading ? null : _handleSendResetEmail,
        isLoading: widget.authStore.isLoading,
      ),
    );
  }

  Widget _buildResendButton() {
    return Observer(
      builder: (context) => AuthButton(
        text: 'Resend Email',
        onPressed: widget.authStore.isLoading ? null : _handleSendResetEmail,
        isLoading: widget.authStore.isLoading,
        isSecondary: true,
      ),
    );
  }

  Widget _buildBackToSignInButton() {
    return TextButton(
      onPressed: () => context.pop(),
      child: Text(
        AuthStrings.backToSignIn,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AuthStrings.emailRequired;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return AuthStrings.emailInvalid;
    }
    
    return null;
  }

  void _handleSendResetEmail() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      await widget.authStore.sendPasswordResetEmail(
        email: _emailController.text.trim(),
      );
      
      if (mounted && !widget.authStore.hasError) {
        setState(() => _emailSent = true);
      }
    } catch (e) {
      // Error is handled by the store and displayed in the UI
    }
  }
}
