import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/auth_strings.dart';

/// Privacy Policy screen displaying the application's privacy policy.
/// 
/// This screen provides a placeholder for the application's privacy policy
/// with proper Material Design styling and navigation.
class AuthPrivacyPolicyScreen extends StatelessWidget {
  const AuthPrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AuthStrings.privacyPolicy),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 24),
              _buildLastUpdated(context),
              const SizedBox(height: 32),
              _buildSection(
                context,
                'Information We Collect',
                'We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support. This may include your name, email address, and other personal information.',
              ),
              _buildSection(
                context,
                'How We Use Your Information',
                'We use the information we collect to provide, maintain, and improve our services, process transactions, send you technical notices and support messages, and communicate with you about products, services, and promotional offers.',
              ),
              _buildSection(
                context,
                'Information Sharing',
                'We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this privacy policy or as required by law.',
              ),
              _buildSection(
                context,
                'Data Security',
                'We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
              ),
              _buildSection(
                context,
                'Data Retention',
                'We retain your personal information for as long as necessary to provide our services, comply with legal obligations, resolve disputes, and enforce our agreements.',
              ),
              _buildSection(
                context,
                'Your Rights',
                'You have the right to access, update, or delete your personal information. You may also have the right to restrict or object to certain processing of your information.',
              ),
              _buildSection(
                context,
                'Cookies and Tracking',
                'We may use cookies and similar tracking technologies to collect information about your use of our services and to provide personalized content and advertisements.',
              ),
              _buildSection(
                context,
                'Third-Party Services',
                'Our services may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties.',
              ),
              _buildSection(
                context,
                'Children\'s Privacy',
                'Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.',
              ),
              _buildSection(
                context,
                'International Data Transfers',
                'Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place for such transfers.',
              ),
              _buildSection(
                context,
                'Changes to This Policy',
                'We may update this privacy policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the "Last Updated" date.',
              ),
              _buildSection(
                context,
                'Contact Us',
                'If you have any questions about this privacy policy or our privacy practices, please contact us through the app or visit our website.',
              ),
              const SizedBox(height: 32),
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AuthStrings.privacyPolicy,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'This privacy policy describes how ${AuthStrings.appName} collects, uses, and protects your personal information.',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildLastUpdated(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.update,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            'Last updated: January 1, 2024',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Your Privacy Matters',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'We are committed to protecting your privacy and ensuring the security of your personal information. If you have any questions or concerns about our privacy practices, please don\'t hesitate to contact us.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
