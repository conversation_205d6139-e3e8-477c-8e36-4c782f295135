import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_user.g.dart';

/// Represents an authenticated user in the BiomeDict application.
/// 
/// This model contains user information retrieved from Firebase Authentication
/// and any additional profile data stored in the application.
@JsonSerializable()
class AuthUser extends Equatable {
  /// Unique identifier for the user
  final String uid;

  /// User's email address
  final String email;

  /// User's display name (optional)
  final String? displayName;

  /// User's first name
  final String? firstName;

  /// User's last name
  final String? lastName;

  /// URL to user's profile photo (optional)
  final String? photoUrl;

  /// Whether the user's email has been verified
  final bool emailVerified;

  /// Whether the user is anonymous
  final bool isAnonymous;

  /// List of authentication providers used by the user
  final List<String> providerIds;

  /// User's phone number (optional)
  final String? phoneNumber;

  /// Timestamp when the user was created
  final DateTime? createdAt;

  /// Timestamp when the user last signed in
  final DateTime? lastSignInAt;

  /// Additional metadata about the user
  final Map<String, dynamic>? metadata;

  const AuthUser({
    required this.uid,
    required this.email,
    this.displayName,
    this.firstName,
    this.lastName,
    this.photoUrl,
    required this.emailVerified,
    required this.isAnonymous,
    required this.providerIds,
    this.phoneNumber,
    this.createdAt,
    this.lastSignInAt,
    this.metadata,
  });

  /// Creates an [AuthUser] from a JSON map
  factory AuthUser.fromJson(Map<String, dynamic> json) => _$AuthUserFromJson(json);

  /// Converts this [AuthUser] to a JSON map
  Map<String, dynamic> toJson() => _$AuthUserToJson(this);

  /// Creates a copy of this [AuthUser] with the given fields replaced
  AuthUser copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? firstName,
    String? lastName,
    String? photoUrl,
    bool? emailVerified,
    bool? isAnonymous,
    List<String>? providerIds,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    Map<String, dynamic>? metadata,
  }) {
    return AuthUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      photoUrl: photoUrl ?? this.photoUrl,
      emailVerified: emailVerified ?? this.emailVerified,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      providerIds: providerIds ?? this.providerIds,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Returns the user's full name if available
  String? get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return displayName;
  }

  /// Returns the user's initials for display purposes
  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0].toUpperCase()}${lastName![0].toUpperCase()}';
    }
    if (displayName != null && displayName!.isNotEmpty) {
      final parts = displayName!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0].toUpperCase()}${parts[1][0].toUpperCase()}';
      }
      return displayName![0].toUpperCase();
    }
    if (email.isNotEmpty) {
      return email[0].toUpperCase();
    }
    return 'U';
  }

  /// Returns whether the user has a complete profile
  bool get hasCompleteProfile {
    return firstName != null && 
           lastName != null && 
           emailVerified;
  }

  /// Returns the primary authentication provider
  String get primaryProvider {
    if (providerIds.isEmpty) return 'unknown';
    return providerIds.first;
  }

  @override
  List<Object?> get props => [
        uid,
        email,
        displayName,
        firstName,
        lastName,
        photoUrl,
        emailVerified,
        isAnonymous,
        providerIds,
        phoneNumber,
        createdAt,
        lastSignInAt,
        metadata,
      ];

  @override
  String toString() {
    return 'AuthUser(uid: $uid, email: $email, emailVerified: $emailVerified)';
  }
}
