import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';

import '../stores/auth_store.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_error_widget.dart';
import '../widgets/auth_loading_overlay.dart';
import '../../../core/constants/auth_strings.dart';
import '../../../core/constants/auth_routes.dart';

/// Sign in screen for user authentication.
/// 
/// This screen provides email/password sign in functionality with
/// proper validation, error handling, and loading states.
class AuthSignInScreen extends StatefulWidget {
  final AuthStore authStore;

  const AuthSignInScreen({
    super.key,
    required this.authStore,
  });

  @override
  State<AuthSignInScreen> createState() => _AuthSignInScreenState();
}

class _AuthSignInScreenState extends State<AuthSignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Observer(
        builder: (context) => AuthLoadingOverlay(
          isLoading: widget.authStore.isLoading,
          loadingMessage: widget.authStore.loadingMessage,
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Spacer(),
                    _buildHeader(),
                    const SizedBox(height: 48),
                    _buildSignInForm(),
                    const SizedBox(height: 24),
                    _buildErrorWidget(),
                    const SizedBox(height: 24),
                    _buildSignInButton(),
                    const SizedBox(height: 16),
                    _buildForgotPasswordButton(),
                    const SizedBox(height: 32),
                    _buildSignUpPrompt(),
                    const Spacer(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Icon(
          Icons.lock_outline,
          size: 64,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 16),
        Text(
          AuthStrings.signIn,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Welcome back to ${AuthStrings.appName}',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSignInForm() {
    return Column(
      children: [
        AuthTextField(
          controller: _emailController,
          focusNode: _emailFocusNode,
          labelText: AuthStrings.email,
          hintText: AuthStrings.emailHint,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.email_outlined,
          validator: _validateEmail,
          onFieldSubmitted: (_) => _passwordFocusNode.requestFocus(),
        ),
        const SizedBox(height: 16),
        AuthTextField(
          controller: _passwordController,
          focusNode: _passwordFocusNode,
          labelText: AuthStrings.password,
          hintText: AuthStrings.passwordHint,
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () => setState(() => _obscurePassword = !_obscurePassword),
          ),
          validator: _validatePassword,
          onFieldSubmitted: (_) => _handleSignIn(),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Observer(
      builder: (context) {
        if (!widget.authStore.hasError) {
          return const SizedBox.shrink();
        }
        
        return AuthErrorWidget(
          message: widget.authStore.lastErrorMessage ?? AuthStrings.genericError,
          onDismiss: widget.authStore.clearError,
        );
      },
    );
  }

  Widget _buildSignInButton() {
    return Observer(
      builder: (context) => AuthButton(
        text: AuthStrings.signIn,
        onPressed: widget.authStore.isLoading ? null : _handleSignIn,
        isLoading: widget.authStore.isLoading,
      ),
    );
  }

  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: () => context.push(AuthRoutes.forgotPassword),
      child: Text(
        AuthStrings.forgotPassword,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSignUpPrompt() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AuthStrings.dontHaveAccount,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(width: 4),
        TextButton(
          onPressed: () => context.push(AuthRoutes.signUp),
          child: Text(
            AuthStrings.signUp,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AuthStrings.emailRequired;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return AuthStrings.emailInvalid;
    }
    
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AuthStrings.passwordRequired;
    }
    
    if (value.length < 6) {
      return AuthStrings.passwordTooShort;
    }
    
    return null;
  }

  void _handleSignIn() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    widget.authStore.signInWithEmailAndPassword(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );
  }
}
