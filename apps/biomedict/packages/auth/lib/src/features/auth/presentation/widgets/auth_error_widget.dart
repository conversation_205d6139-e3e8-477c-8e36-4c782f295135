import 'package:flutter/material.dart';

/// Widget for displaying authentication errors in a consistent manner.
/// 
/// This widget provides a Material Design error display with proper
/// styling, accessibility, and dismissal functionality.
class AuthErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;
  final VoidCallback? onRetry;
  final IconData? icon;
  final bool showDismissButton;
  final bool showRetryButton;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const AuthErrorWidget({
    super.key,
    required this.message,
    this.onDismiss,
    this.onRetry,
    this.icon,
    this.showDismissButton = true,
    this.showRetryButton = false,
    this.margin,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon ?? Icons.error_outline,
            color: theme.colorScheme.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  message,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onErrorContainer,
                  ),
                ),
                if (showRetryButton && onRetry != null) ...[
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: onRetry,
                    style: TextButton.styleFrom(
                      foregroundColor: theme.colorScheme.error,
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(0, 32),
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ],
            ),
          ),
          if (showDismissButton && onDismiss != null)
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: theme.colorScheme.error,
                size: 18,
              ),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: EdgeInsets.zero,
              tooltip: 'Dismiss error',
            ),
        ],
      ),
    );
  }
}

/// Snackbar variant for showing authentication errors
class AuthErrorSnackBar extends SnackBar {
  AuthErrorSnackBar({
    super.key,
    required String message,
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) : super(
          content: Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[700],
          duration: duration,
          action: onRetry != null
              ? SnackBarAction(
                  label: 'Retry',
                  textColor: Colors.white,
                  onPressed: onRetry,
                )
              : null,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
}

/// Dialog variant for showing critical authentication errors
class AuthErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final String? retryButtonText;
  final String? dismissButtonText;

  const AuthErrorDialog({
    super.key,
    this.title = 'Error',
    required this.message,
    this.onRetry,
    this.onDismiss,
    this.retryButtonText = 'Retry',
    this.dismissButtonText = 'OK',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      icon: Icon(
        Icons.error_outline,
        color: theme.colorScheme.error,
        size: 32,
      ),
      title: Text(
        title,
        style: theme.textTheme.headlineSmall?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
      ),
      content: Text(
        message,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
      actions: [
        if (onRetry != null)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry?.call();
            },
            child: Text(retryButtonText!),
          ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onDismiss?.call();
          },
          child: Text(dismissButtonText!),
        ),
      ],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  /// Show the error dialog
  static Future<void> show(
    BuildContext context, {
    String title = 'Error',
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    String? retryButtonText = 'Retry',
    String? dismissButtonText = 'OK',
  }) {
    return showDialog<void>(
      context: context,
      builder: (context) => AuthErrorDialog(
        title: title,
        message: message,
        onRetry: onRetry,
        onDismiss: onDismiss,
        retryButtonText: retryButtonText,
        dismissButtonText: dismissButtonText,
      ),
    );
  }
}

/// Extension for easy error display
extension AuthErrorExtension on BuildContext {
  /// Show error snackbar
  void showAuthError(String message, {VoidCallback? onRetry}) {
    ScaffoldMessenger.of(this).showSnackBar(
      AuthErrorSnackBar(
        message: message,
        onRetry: onRetry,
      ),
    );
  }

  /// Show error dialog
  Future<void> showAuthErrorDialog(
    String message, {
    String title = 'Error',
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    return AuthErrorDialog.show(
      this,
      title: title,
      message: message,
      onRetry: onRetry,
      onDismiss: onDismiss,
    );
  }
}
