/// Authentication route constants for the BiomeDict app.
/// 
/// This class defines all route paths used in the authentication flow
/// to maintain consistency and support deep linking.
class AuthRoutes {
  AuthRoutes._();

  // Root paths
  static const String root = '/';
  static const String auth = '/auth';

  // Authentication routes
  static const String signIn = '/auth/sign-in';
  static const String signUp = '/auth/sign-up';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String emailVerification = '/auth/email-verification';

  // Profile routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';

  // Legal routes
  static const String termsOfService = '/legal/terms';
  static const String privacyPolicy = '/legal/privacy';

  // Protected app routes (examples)
  static const String dashboard = '/dashboard';
  static const String home = '/home';
  static const String settings = '/settings';

  // Route names for navigation
  static const String signInName = 'sign-in';
  static const String signUpName = 'sign-up';
  static const String forgotPasswordName = 'forgot-password';
  static const String resetPasswordName = 'reset-password';
  static const String emailVerificationName = 'email-verification';
  static const String profileName = 'profile';
  static const String editProfileName = 'edit-profile';
  static const String termsOfServiceName = 'terms-of-service';
  static const String privacyPolicyName = 'privacy-policy';
  static const String dashboardName = 'dashboard';
  static const String homeName = 'home';
  static const String settingsName = 'settings';

  /// List of routes that require authentication
  static const List<String> protectedRoutes = [
    dashboard,
    home,
    profile,
    editProfile,
    settings,
  ];

  /// List of authentication-related routes
  static const List<String> authRoutes = [
    signIn,
    signUp,
    forgotPassword,
    resetPassword,
    emailVerification,
  ];

  /// List of public routes that don't require authentication
  static const List<String> publicRoutes = [
    root,
    termsOfService,
    privacyPolicy,
  ];

  /// Check if a route requires authentication
  static bool isProtectedRoute(String route) {
    return protectedRoutes.contains(route);
  }

  /// Check if a route is authentication-related
  static bool isAuthRoute(String route) {
    return authRoutes.contains(route);
  }

  /// Check if a route is public
  static bool isPublicRoute(String route) {
    return publicRoutes.contains(route);
  }

  /// Get the default route for authenticated users
  static String get defaultAuthenticatedRoute => dashboard;

  /// Get the default route for unauthenticated users
  static String get defaultUnauthenticatedRoute => signIn;
}
