/// Authentication related string constants for the BiomeDict app.
/// 
/// This class centralizes all user-facing strings used in the authentication
/// flow to support future localization and maintain consistency.
class AuthStrings {
  AuthStrings._();

  // App Information
  static const String appName = 'BiomeDict';
  static const String appDescription = 'Your personal health companion';

  // Authentication Actions
  static const String signIn = 'Sign In';
  static const String signUp = 'Sign Up';
  static const String signOut = 'Sign Out';
  static const String forgotPassword = 'Forgot Password?';
  static const String resetPassword = 'Reset Password';
  static const String sendResetEmail = 'Send Reset Email';
  static const String backToSignIn = 'Back to Sign In';
  static const String createAccount = 'Create Account';
  static const String alreadyHaveAccount = 'Already have an account?';
  static const String dontHaveAccount = "Don't have an account?";

  // Form Fields
  static const String email = 'Email';
  static const String emailHint = 'Enter your email address';
  static const String password = 'Password';
  static const String passwordHint = 'Enter your password';
  static const String confirmPassword = 'Confirm Password';
  static const String confirmPasswordHint = 'Confirm your password';
  static const String firstName = 'First Name';
  static const String firstNameHint = 'Enter your first name';
  static const String lastName = 'Last Name';
  static const String lastNameHint = 'Enter your last name';

  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email address';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort = 'Password must be at least 6 characters';
  static const String passwordsDoNotMatch = 'Passwords do not match';
  static const String firstNameRequired = 'First name is required';
  static const String lastNameRequired = 'Last name is required';

  // Email Verification
  static const String emailVerification = 'Email Verification';
  static const String emailVerificationRequired = 'Email Verification Required';
  static const String emailVerificationSent = 'Verification email sent';
  static const String emailVerificationDescription = 
      'We\'ve sent a verification email to your address. Please check your inbox and click the verification link to continue.';
  static const String emailNotVerified = 'Email not verified';
  static const String resendVerificationEmail = 'Resend Verification Email';
  static const String checkEmailAndContinue = 'Check Email & Continue';
  static const String emailVerified = 'Email verified successfully';

  // Password Reset
  static const String passwordResetTitle = 'Reset Your Password';
  static const String passwordResetDescription = 
      'Enter your email address and we\'ll send you a link to reset your password.';
  static const String passwordResetEmailSent = 'Password reset email sent';
  static const String passwordResetEmailDescription = 
      'We\'ve sent a password reset link to your email address. Please check your inbox and follow the instructions.';

  // Error Messages
  static const String genericError = 'An error occurred. Please try again.';
  static const String networkError = 'Network error. Please check your connection.';
  static const String userNotFound = 'No user found with this email address.';
  static const String wrongPassword = 'Incorrect password. Please try again.';
  static const String emailAlreadyInUse = 'An account already exists with this email address.';
  static const String weakPassword = 'Password is too weak. Please choose a stronger password.';
  static const String invalidEmail = 'Invalid email address format.';
  static const String userDisabled = 'This account has been disabled.';
  static const String tooManyRequests = 'Too many requests. Please try again later.';
  static const String operationNotAllowed = 'This operation is not allowed.';

  // Success Messages
  static const String signInSuccess = 'Signed in successfully';
  static const String signUpSuccess = 'Account created successfully';
  static const String signOutSuccess = 'Signed out successfully';
  static const String passwordResetSuccess = 'Password reset email sent successfully';

  // Loading States
  static const String signingIn = 'Signing in...';
  static const String signingUp = 'Creating account...';
  static const String signingOut = 'Signing out...';
  static const String sendingResetEmail = 'Sending reset email...';
  static const String verifyingEmail = 'Verifying email...';

  // Terms and Privacy
  static const String termsOfService = 'Terms of Service';
  static const String privacyPolicy = 'Privacy Policy';
  static const String agreeToTerms = 'By creating an account, you agree to our';
  static const String and = 'and';

  // Accessibility
  static const String emailFieldSemantics = 'Email address input field';
  static const String passwordFieldSemantics = 'Password input field';
  static const String signInButtonSemantics = 'Sign in button';
  static const String signUpButtonSemantics = 'Sign up button';
  static const String forgotPasswordSemantics = 'Forgot password link';
  static const String backButtonSemantics = 'Go back';

  // Biometric Authentication (for future use)
  static const String biometricSignIn = 'Sign in with biometrics';
  static const String biometricNotAvailable = 'Biometric authentication not available';
  static const String biometricNotEnrolled = 'No biometric credentials enrolled';

  // Social Authentication (for future use)
  static const String signInWithGoogle = 'Sign in with Google';
  static const String signInWithApple = 'Sign in with Apple';
  static const String signInWithFacebook = 'Sign in with Facebook';

  // Profile Management
  static const String profile = 'Profile';
  static const String editProfile = 'Edit Profile';
  static const String updateProfile = 'Update Profile';
  static const String profileUpdated = 'Profile updated successfully';
  static const String deleteAccount = 'Delete Account';
  static const String deleteAccountConfirmation = 
      'Are you sure you want to delete your account? This action cannot be undone.';
  static const String accountDeleted = 'Account deleted successfully';

  // Session Management
  static const String sessionExpired = 'Your session has expired. Please sign in again.';
  static const String sessionExpiring = 'Your session is about to expire.';
  static const String extendSession = 'Extend Session';
}
