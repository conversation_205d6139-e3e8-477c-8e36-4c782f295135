import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import 'package:di/di.dart';

import '../../features/auth/data/repositories/firebase_auth_repository.dart';
import '../../features/auth/data/mappers/auth_user_mapper.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/domain/services/auth_service.dart';
import '../../features/auth/presentation/stores/auth_store.dart';
import '../../features/auth/presentation/guards/auth_guard.dart';

/// Dependency injection setup for the authentication package.
/// 
/// This class handles the registration and initialization of all
/// authentication-related dependencies using the DI package.
class AuthDI {
  static final Logger _logger = Logger();
  static bool _isInitialized = false;
  static DependencyInjection? _dependencyInjection;

  /// Initialize authentication dependencies
  static Future<void> initialize({
    DependencyInjection? di,
    FirebaseAuth? firebaseAuth,
    Logger? logger,
  }) async {
    if (_isInitialized) {
      _logger.w('AuthDI: Already initialized, skipping...');
      return;
    }

    try {
      _logger.i('AuthDI: Initializing authentication dependencies');

      // Use provided DI instance or create a new one
      _dependencyInjection = di ?? GetItDependencyInjection();

      // Initialize DI if not already done
      if (!_dependencyInjection!.isInitialized) {
        await _dependencyInjection!.init();
      }

      // Register core dependencies
      await _registerCoreDependencies(
        _dependencyInjection!,
        firebaseAuth,
        logger,
      );

      // Register data layer dependencies
      await _registerDataDependencies(_dependencyInjection!);

      // Register domain layer dependencies
      await _registerDomainDependencies(_dependencyInjection!);

      // Register presentation layer dependencies
      await _registerPresentationDependencies(_dependencyInjection!);

      _isInitialized = true;
      _logger.i('AuthDI: Authentication dependencies initialized successfully');
    } catch (e, stackTrace) {
      _logger.e('AuthDI: Failed to initialize dependencies', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Register core dependencies (Firebase, Logger, etc.)
  static Future<void> _registerCoreDependencies(
    DependencyInjection di,
    FirebaseAuth? firebaseAuth,
    Logger? logger,
  ) async {
    _logger.d('AuthDI: Registering core dependencies');

    // Register Firebase Auth instance
    final authInstance = firebaseAuth ?? FirebaseAuth.instance;
    di.register<FirebaseAuth>(authInstance);

    // Register Logger instance
    final loggerInstance = logger ?? Logger();
    di.register<Logger>(loggerInstance);

    _logger.d('AuthDI: Core dependencies registered');
  }

  /// Register data layer dependencies
  static Future<void> _registerDataDependencies(
    DependencyInjection di,
  ) async {
    _logger.d('AuthDI: Registering data layer dependencies');

    // Register AuthUserMapper
    const authUserMapper = AuthUserMapper();
    di.register<AuthUserMapper>(authUserMapper);

    // Register AuthRepository implementation
    final authRepository = FirebaseAuthRepository(
      firebaseAuth: di.get<FirebaseAuth>(),
      logger: di.get<Logger>(),
      userMapper: di.get<AuthUserMapper>(),
    );
    di.register<AuthRepository>(
      authRepository,
      dispose: (repo) => {}, // No specific disposal needed
    );

    _logger.d('AuthDI: Data layer dependencies registered');
  }

  /// Register domain layer dependencies
  static Future<void> _registerDomainDependencies(
    DependencyInjection di,
  ) async {
    _logger.d('AuthDI: Registering domain layer dependencies');

    // AuthService will be registered after AuthStore since it depends on it
    // This is handled in _registerPresentationDependencies

    _logger.d('AuthDI: Domain layer dependencies registered');
  }

  /// Register presentation layer dependencies
  static Future<void> _registerPresentationDependencies(
    DependencyInjection di,
  ) async {
    _logger.d('AuthDI: Registering presentation layer dependencies');

    // Register AuthStore
    final authStore = AuthStore(
      authRepository: di.get<AuthRepository>(),
      logger: di.get<Logger>(),
    );
    di.register<AuthStore>(
      authStore,
      dispose: (store) => store.dispose(),
    );

    // Register AuthService
    final authService = AuthService(
      authRepository: di.get<AuthRepository>(),
      authStore: di.get<AuthStore>(),
      logger: di.get<Logger>(),
    );
    di.register<AuthService>(
      authService,
      dispose: (service) => service.dispose(),
    );

    // Register AuthGuard
    final authGuard = AuthGuard(
      authStore: di.get<AuthStore>(),
      logger: di.get<Logger>(),
    );
    di.register<AuthGuard>(authGuard);

    _logger.d('AuthDI: Presentation layer dependencies registered');
  }

  /// Get a dependency from the DI container
  static T get<T extends Object>() {
    if (!_isInitialized || _dependencyInjection == null) {
      throw StateError('AuthDI: Dependencies not initialized. Call AuthDI.initialize() first.');
    }

    try {
      return _dependencyInjection!.get<T>();
    } catch (e) {
      _logger.e('AuthDI: Failed to get dependency of type $T: $e');
      rethrow;
    }
  }

  /// Check if a dependency is registered
  static bool has<T extends Object>() {
    if (!_isInitialized || _dependencyInjection == null) {
      return false;
    }

    try {
      return _dependencyInjection!.has<T>();
    } catch (e) {
      _logger.e('AuthDI: Failed to check dependency of type $T: $e');
      return false;
    }
  }

  /// Dispose all authentication dependencies
  static Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.w('AuthDI: Not initialized, nothing to dispose');
      return;
    }

    try {
      _logger.i('AuthDI: Disposing authentication dependencies');

      if (_dependencyInjection != null) {
        // Dispose in reverse order of registration
        if (_dependencyInjection!.has<AuthGuard>()) {
          await _dependencyInjection!.unregister<AuthGuard>(null);
        }

        if (_dependencyInjection!.has<AuthService>()) {
          await _dependencyInjection!.unregister<AuthService>(null);
        }

        if (_dependencyInjection!.has<AuthStore>()) {
          await _dependencyInjection!.unregister<AuthStore>(null);
        }

        if (_dependencyInjection!.has<AuthRepository>()) {
          await _dependencyInjection!.unregister<AuthRepository>(null);
        }

        if (_dependencyInjection!.has<AuthUserMapper>()) {
          await _dependencyInjection!.unregister<AuthUserMapper>(null);
        }

        await _dependencyInjection!.dispose();
        _dependencyInjection = null;
      }

      _isInitialized = false;
      _logger.i('AuthDI: Authentication dependencies disposed successfully');
    } catch (e, stackTrace) {
      _logger.e('AuthDI: Failed to dispose dependencies', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Reset all authentication dependencies
  static Future<void> reset() async {
    await dispose();
    await initialize();
  }

  /// Check if dependencies are initialized
  static bool get isInitialized => _isInitialized;

  /// Get the current auth store instance
  static AuthStore get authStore => get<AuthStore>();

  /// Get the current auth service instance
  static AuthService get authService => get<AuthService>();

  /// Get the current auth guard instance
  static AuthGuard get authGuard => get<AuthGuard>();

  /// Get the current auth repository instance
  static AuthRepository get authRepository => get<AuthRepository>();
}
