/// Firebase Authentication package for BiomeDict
///
/// This package provides comprehensive Firebase Authentication functionality
/// with MobX state management, go_router integration, and Material Design UI.
library auth;

// Core exports
export 'src/core/constants/auth_strings.dart';
export 'src/core/constants/auth_routes.dart';
export 'src/core/di/auth_di.dart';

// Domain models
export 'src/features/auth/domain/models/auth_user.dart';
export 'src/features/auth/domain/models/auth_state.dart';
export 'src/features/auth/domain/models/auth_exception.dart';

// Domain interfaces
export 'src/features/auth/domain/repositories/auth_repository.dart';
export 'src/features/auth/domain/services/auth_service.dart';

// Data layer
export 'src/features/auth/data/repositories/firebase_auth_repository.dart';
export 'src/features/auth/data/mappers/auth_user_mapper.dart';

// Presentation layer
export 'src/features/auth/presentation/stores/auth_store.dart';
export 'src/features/auth/presentation/guards/auth_guard.dart';
export 'src/features/auth/presentation/router/auth_router.dart';

// Screens
export 'src/features/auth/presentation/screens/sign_in_screen.dart';
export 'src/features/auth/presentation/screens/sign_up_screen.dart';
export 'src/features/auth/presentation/screens/forgot_password_screen.dart';
export 'src/features/auth/presentation/screens/email_verification_screen.dart';
export 'src/features/auth/presentation/screens/terms_of_service_screen.dart';
export 'src/features/auth/presentation/screens/privacy_policy_screen.dart';

// Widgets
export 'src/features/auth/presentation/widgets/auth_text_field.dart';
export 'src/features/auth/presentation/widgets/auth_button.dart';
export 'src/features/auth/presentation/widgets/auth_error_widget.dart';
export 'src/features/auth/presentation/widgets/auth_loading_overlay.dart';
