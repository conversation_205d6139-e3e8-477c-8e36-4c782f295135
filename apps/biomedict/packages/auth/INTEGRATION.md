# Integration Guide

This guide explains how to integrate the Firebase Authentication package into your BiomeDict application.

## Prerequisites

Before integrating the auth package, ensure you have:

1. **Firebase Project Setup**
   - Created a Firebase project
   - Enabled Authentication with Email/Password provider
   - Added your app to the Firebase project (Android/iOS)
   - Downloaded configuration files (`google-services.json`, `GoogleService-Info.plist`)

2. **Flutter Dependencies**
   - Flutter SDK 3.0.0 or higher
   - Dart SDK 3.7.2 or higher

## Step-by-Step Integration

### 1. Add Package Dependency

Add the auth package to your app's `pubspec.yaml`:

```yaml
dependencies:
  auth:
    path: ../../packages/auth
```

Run `flutter pub get` to install dependencies.

### 2. Initialize Firebase and Auth

Update your `main.dart` file:

```dart
import 'package:auth/auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // Initialize auth dependencies
  await AuthDI.initialize();
  
  runApp(MyApp());
}
```

### 3. Configure Router with Auth Guards

Create or update your router configuration:

```dart
import 'package:auth/auth.dart';
import 'package:go_router/go_router.dart';

class AppRouter {
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: AuthRoutes.signIn,
      redirect: (context, state) {
        // Apply auth guard redirect logic
        return AuthDI.authGuard.redirectLogic(context, state);
      },
      routes: [
        // Include all auth routes
        ...AuthRouter.routes,
        
        // Your protected app routes
        GoRoute(
          path: '/dashboard',
          name: 'dashboard',
          builder: (context, state) => DashboardScreen(),
        ),
        GoRoute(
          path: '/profile',
          name: 'profile',
          builder: (context, state) => ProfileScreen(),
        ),
        // Add more routes as needed
      ],
    );
  }
}
```

### 4. Update Your App Widget

Modify your main app widget to use the router:

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'BiomeDict',
      theme: ThemeData(
        // Your theme configuration
      ),
      routerConfig: AppRouter.createRouter(),
    );
  }
}
```

### 5. Access Authentication State

Use the auth services in your widgets:

```dart
import 'package:auth/auth.dart';
import 'package:flutter_mobx/flutter_mobx.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        final authStore = AuthDI.authStore;
        
        if (authStore.isLoading) {
          return CircularProgressIndicator();
        }
        
        if (authStore.isAuthenticated) {
          return Text('Welcome, ${authStore.currentUser?.email}!');
        }
        
        return Text('Please sign in');
      },
    );
  }
}
```

### 6. Handle Authentication Actions

Implement sign in/out functionality:

```dart
class AuthActions {
  static Future<void> signIn(String email, String password) async {
    try {
      await AuthDI.authService.signIn(
        email: email,
        password: password,
      );
    } on AuthException catch (e) {
      // Handle authentication errors
      print('Sign in failed: ${e.message}');
    }
  }
  
  static Future<void> signOut() async {
    try {
      await AuthDI.authService.signOut();
    } catch (e) {
      print('Sign out failed: $e');
    }
  }
}
```

## Route Configuration

### Protected Routes

Define which routes require authentication:

```dart
// These routes will redirect to sign-in if user is not authenticated
static const List<String> protectedRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
  '/data',
];
```

### Public Routes

Define routes accessible without authentication:

```dart
// These routes are always accessible
static const List<String> publicRoutes = [
  '/',
  '/about',
  '/legal/terms',
  '/legal/privacy',
];
```

### Auth Routes

Authentication-related routes that redirect authenticated users:

```dart
// These routes redirect to dashboard if user is already authenticated
static const List<String> authRoutes = [
  '/auth/sign-in',
  '/auth/sign-up',
  '/auth/forgot-password',
  '/auth/email-verification',
];
```

## Customization Options

### Custom Theme Integration

The auth package respects your app's Material Theme:

```dart
MaterialApp.router(
  theme: ThemeData(
    colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
    // Auth components will use these colors automatically
  ),
  routerConfig: AppRouter.createRouter(),
)
```

### Custom Error Handling

Override default error handling:

```dart
class CustomAuthService extends AuthService {
  @override
  Future<AuthUser> signIn({
    required String email,
    required String password,
  }) async {
    try {
      return await super.signIn(email: email, password: password);
    } on AuthException catch (e) {
      // Log to analytics
      Analytics.logError('auth_sign_in_failed', e.code);
      
      // Show custom error UI
      NotificationService.showError(e.message);
      
      rethrow;
    }
  }
}

// Register custom service
await AuthDI.initialize();
// Replace the default service
di.register<AuthService>(CustomAuthService(...));
```

### Custom UI Components

Use auth components in your custom screens:

```dart
class CustomSignInScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          children: [
            // Your custom header
            CustomHeader(),
            
            // Use auth components
            AuthTextField(
              labelText: 'Email Address',
              prefixIcon: Icons.email,
              keyboardType: TextInputType.emailAddress,
            ),
            SizedBox(height: 16),
            AuthPasswordField(
              labelText: 'Password',
            ),
            SizedBox(height: 24),
            AuthButton(
              text: 'Sign In',
              onPressed: () => _handleSignIn(),
            ),
            
            // Your custom footer
            CustomFooter(),
          ],
        ),
      ),
    );
  }
}
```

## Testing Integration

### Unit Tests

Test authentication logic:

```dart
void main() {
  group('Auth Integration Tests', () {
    late AuthService authService;
    
    setUp(() async {
      await AuthDI.initialize();
      authService = AuthDI.authService;
    });
    
    test('should sign in with valid credentials', () async {
      final user = await authService.signIn(
        email: '<EMAIL>',
        password: 'password123',
      );
      
      expect(user.email, '<EMAIL>');
      expect(authService.isAuthenticated, true);
    });
  });
}
```

### Widget Tests

Test UI integration:

```dart
void main() {
  testWidgets('should show sign in screen when not authenticated', (tester) async {
    await AuthDI.initialize();
    
    await tester.pumpWidget(
      MaterialApp.router(
        routerConfig: AppRouter.createRouter(),
      ),
    );
    
    expect(find.text('Sign In'), findsOneWidget);
    expect(find.byType(AuthTextField), findsWidgets);
  });
}
```

## Troubleshooting

### Common Issues

1. **Firebase not initialized**
   ```
   Error: Firebase has not been initialized
   ```
   Solution: Ensure `Firebase.initializeApp()` is called before `AuthDI.initialize()`

2. **Route redirect loops**
   ```
   Error: Too many redirects
   ```
   Solution: Check your route configuration and ensure auth routes don't conflict

3. **Dependencies not found**
   ```
   Error: No implementation found for AuthService
   ```
   Solution: Ensure `AuthDI.initialize()` is called before accessing services

### Debug Mode

Enable debug logging:

```dart
await AuthDI.initialize(
  logger: Logger(
    level: Level.debug,
    printer: PrettyPrinter(),
  ),
);
```

### Performance Considerations

- Auth state is cached and persists across app restarts
- Route guards are lightweight and don't block navigation
- UI components are optimized for 60fps performance

## Migration Guide

If migrating from another auth solution:

1. **Export existing user data** from your current system
2. **Import users to Firebase** using Firebase Admin SDK
3. **Update route definitions** to use auth package routes
4. **Replace auth UI components** with auth package components
5. **Test authentication flows** thoroughly

## Support

For issues and questions:

1. Check the [API documentation](API.md)
2. Review [example implementations](example/)
3. Search existing issues in the repository
4. Create a new issue with detailed reproduction steps
